import 'dart:convert';

import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/model/js_bridge_bean.dart';
import 'package:lib_base/model/js_bridge_data_bean.dart';
import 'package:lib_base/model/user_info_model.dart';
import 'package:lib_base/pages/webview/webview_mxin/method_handler_detail/method_handler_detail.dart';

class GetUserInfoFromAppHandler extends EyybAssetWebHandler {
  @override
  handlerCallback(InAppWebViewController webViewController, WidgetRef ref,
      JsBridgeBean? jsBridgeBean, List<dynamic> arguments) {
    if (jsBridgeBean != null) {
      UserInfoModel userInfoModel = UserInfoModel.loadDefault();
     
      var callbackId = jsBridgeBean.callbackId;
      JsBridgeDataBean data = JsBridgeDataBean();
       // id,loginName,memberId,memberName,overDate,vipBookVersion,businessId,businessName,nickName,sex,photo,areaCode,areaName,schoolId,schoolName,gradeId,gradeName,cnVersion,enVersion,mathVersion,point,ecity,eprovince,eaddress,studentName,name,nickname,segment
      // var resultMap = {
      //   "id": userInfoModel.otherInfo?.id,
      //   "loginName": userInfoModel.otherInfo?.loginName,
      //   "memberId": userInfoModel.obj?.memberCode,
      //   "memberName": userInfoModel.otherInfo?.memberName,
      //   "overDate": userInfoModel.otherInfo?.overDate,
      //   "vipBookVersion": userInfoModel.otherInfo?.vipBookVersion,
      //   "businessId": userInfoModel.otherInfo?.businessId,
      //   "businessName": userInfoModel.otherInfo?.businessName,
      //   "nickName": userInfoModel.otherInfo?.nickname,
      //   "sex": userInfoModel.otherInfo?.sex,
      //   "photo": userInfoModel.otherInfo?.photo,
      //   "areaCode": userInfoModel.otherInfo?.areaCode,
      //   "areaName": userInfoModel.otherInfo?.areaName,
      //   "schoolId": userInfoModel.otherInfo?.schoolId,
      //   "schoolName": userInfoModel.otherInfo?.schoolName,
      //   "gradeId": userInfoModel.otherInfo?.gradeId,
      //   "gradeName": userInfoModel.otherInfo?.gradeName,
      //   "cnVersion": userInfoModel.otherInfo?.cnVersion,
      //   "enVersion": userInfoModel.otherInfo?.enVersion,
      //   "mathVersion": userInfoModel.otherInfo?.mathVersion,
      //   "point": userInfoModel.otherInfo?.point,
      //   "ecity": userInfoModel.otherInfo?.ecity,
      //   "eprovince": userInfoModel.otherInfo?.eprovince,
      //   "eaddress": userInfoModel.otherInfo?.eaddress,
      //   "studentName": userInfoModel.otherInfo?.studentName,
      //   "name": userInfoModel.otherInfo?.name,
      //   "nickname": userInfoModel.otherInfo?.nickname,
      //   "segment": userInfoModel.otherInfo?.segment,
      // };
      data.id= userInfoModel.otherInfo?.id;
      data.loginName= userInfoModel.obj?.loginName;
      data.memberId= userInfoModel.obj?.memberCode;
      data.memberName= userInfoModel.obj?.memberName;
      data.overDate= userInfoModel.obj?.overDate;
      data.vipBookVersion= userInfoModel.obj?.vipBookVersion;
      data.businessId= userInfoModel.otherInfo?.businessId;
      data.businessName= userInfoModel.otherInfo?.businessName;
      data.nickName= userInfoModel.obj?.nickname;
      data.sex= userInfoModel.obj?.sex?.toInt();
      data.photo= userInfoModel.obj?.photo;
      data.areaCode= userInfoModel.obj?.areaCode;
      data.areaName= userInfoModel.obj?.areaName;
      data.schoolId= userInfoModel.obj?.schoolId;
      data.schoolName= userInfoModel.obj?.schoolName;
      data.gradeId= userInfoModel.obj?.gradeId;
      data.gradeName= userInfoModel.obj?.gradeName;
      data.cnVersion= userInfoModel.obj?.cnVersion;
      data.enVersion= userInfoModel.obj?.enVersion;
      data.mathVersion= userInfoModel.obj?.mathVersion;
      data.point= userInfoModel.obj?.point?.toInt();
      data.ecity= userInfoModel.obj?.ecity;
      data.eprovince= userInfoModel.obj?.eprovince;
      data.eaddress= userInfoModel.obj?.eaddress;
      data.studentName= userInfoModel.otherInfo?.studentName;
      data.name= userInfoModel.obj?.name;
      data.nickname= userInfoModel.obj?.nickname;
      data.segment= userInfoModel.obj?.segment;



      Logger.info("============getUserInfoFromApp get user data: $data");
      sendToJs(webViewController,
          methodName: "setUserInfoFromApp", data: data, callbackId: callbackId);
    }
  }

  @override
  String get methodName => 'getUserInfoFromApp';
}
