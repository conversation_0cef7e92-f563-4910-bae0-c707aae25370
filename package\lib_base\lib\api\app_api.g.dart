// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_api.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers

class _BaseAppApi implements BaseAppApi {
  _BaseAppApi(
    this._dio, {
    this.baseUrl,
  });

  final Dio _dio;

  String? baseUrl;

  @override
  Future<BaseResponse<List<EyybAppVersionModel>>> eyyb_app_version() async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': true,
      'needErrorToast': true,
      'host_name': 'webHost',
      'ignoreResponseLog': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<EyybAppVersionModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/v2/eyyb_app/eyyb_app_version.json',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<EyybAppVersionModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<EyybAppVersionModel>((i) =>
                  EyybAppVersionModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<String>> login(
    String loginName,
    String loginPwd,
    String deviceId,
    String phonePlatform,
    int appSource,
    String appId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': true,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'loginName': loginName,
      'loginPwd': loginPwd,
      'deviceId': deviceId,
      'phonePlatform': phonePlatform,
      'appSource': appSource,
      'appId': appId,
    };
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<String>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/login/api/login',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<String>.fromJson(
      _result.data!,
      (json) => json as String,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> sendMsgCode(
    String userPhoneNo,
    String codeType,
    int userType,
    String businessId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': true,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'userPhoneNo': userPhoneNo,
      'codeType': codeType,
      'userType': userType,
      'businessId': businessId,
    };
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/news/v4_1/sendMsgCode',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> verifyResetPass(
    String username,
    String password,
    String verifyCode,
    int userType,
    String appId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': true,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'username': username,
      'password': password,
      'verifyCode': verifyCode,
      'userType': userType,
      'appId': appId,
    };
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/user/api/verifyResetPass',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<String>> register(
    String username,
    String password,
    String deviceId,
    String phonePlatform,
    String appSource,
    String appId,
    String verifyCode,
    String userType,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': true,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'username': username,
      'password': password,
      'deviceId': deviceId,
      'phonePlatform': phonePlatform,
      'appSource': appSource,
      'appId': appId,
      'verifyCode': verifyCode,
      'userType': userType,
    };
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<String>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/login/api/register',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<String>.fromJson(
      _result.data!,
      (json) => json as String,
    );
    return value;
  }

  @override
  Future<BaseResponse<PUserInfoEntity>> getPuserInfo() async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<PUserInfoEntity>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/user/api/getPuserInfo',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<PUserInfoEntity>.fromJson(
      _result.data!,
      (json) => PUserInfoEntity.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<SignInfo>> getSignInfo(
    String userId,
    String? years,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'years': years,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<SignInfo>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/sign/api/getSignInfo',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<SignInfo>.fromJson(
      _result.data!,
      (json) => SignInfo.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<WalletInfo>> findWallet(String userId) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': false,
      'host_name': 'payHost',
      'host_port': '',
    };
    final queryParameters = <String, dynamic>{r'userId': userId};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<WalletInfo>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/pay-api/api/yyb/v1/usermoney/api/findWallet',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<WalletInfo>.fromJson(
      _result.data!,
      (json) => WalletInfo.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<QueryMyResourceModel>> queryMyResouce(
    List<String> types,
    List<String> resourceIds,
    List<String> moduleIds,
    String expired,
    String puserId,
    int pageSize,
    int pageIndex,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{
      r'types': types,
      r'resourceIds': resourceIds,
      r'moduleIds': moduleIds,
      r'expired': expired,
      r'puserId': puserId,
      r'pageSize': pageSize,
      r'pageIndex': pageIndex,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<QueryMyResourceModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/order-api/api/yyb/v1/userbuy/api/queryMyResouce',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<QueryMyResourceModel>.fromJson(
      _result.data!,
      (json) => QueryMyResourceModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<DicItemModel>>> selectwordtonelist() async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': false,
      'ignoreResponseLog': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<DicItemModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/manmachinetalkapi/selectwordtonelist',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<DicItemModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<DicItemModel>(
                  (i) => DicItemModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<AdInfo>>> queryAvailableList(
    String? positionId,
    String? userLevelCode,
    String? appInnerVersion,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'positionId': positionId,
      r'userLevelCode': userLevelCode,
      r'appInnerVersion': appInnerVersion,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<AdInfo>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/advertise-api/api/yyb/v1/advertise/info/api/queryAvailableList',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<AdInfo>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<AdInfo>((i) => AdInfo.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<AreaInfo>>> queryAllRegions() async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<AreaInfo>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/school-api/api/yyb/v1/schoolApi/queryAllRegions',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<AreaInfo>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<AreaInfo>(
                  (i) => AreaInfo.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> upuserarea(
    String? areaCode,
    String? userId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'areaCode': areaCode,
      'userId': userId,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/user/v50/user/api/upuserarea',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<UserOtherInfoModel>> getUserWithOtherInfo(
      String? userId) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'userId': userId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<UserOtherInfoModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/user/api/getUserWithOtherInfo',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<UserOtherInfoModel>.fromJson(
      _result.data!,
      (json) => UserOtherInfoModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> updateUser(
    String userId,
    String? schoolId,
    String? gradeId,
    String? gradeName,
    String? enVersion,
    String? areaCode,
    String? segment,
    String? nickName,
    String? sex,
    String? mathVersion,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'userId': userId,
      'schoolId': schoolId,
      'gradeId': gradeId,
      'gradeName': gradeName,
      'enVersion': enVersion,
      'areaCode': areaCode,
      'segment': segment,
      'nickName': nickName,
      'sex': sex,
      'mathVersion': mathVersion,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/user/api/updateUser',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<ClazzDicInfoModel>>> queryClazzNum(
      String teacherId) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'teacherId': teacherId};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<ClazzDicInfoModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/clazzTeacher/queryClazzNum',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<ClazzDicInfoModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<ClazzDicInfoModel>(
                  (i) => ClazzDicInfoModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<BookInfo>>> querybook(
    String? version,
    String? grade,
    String? objective,
    String subject,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
      'ignoreResponseLog': true,
    };
    final queryParameters = <String, dynamic>{
      r'version': version,
      r'grade': grade,
      r'objective': objective,
      r'subject': subject,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<BookInfo>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/textbook-api/api/textbook/v50/book/querybook',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<BookInfo>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<BookInfo>(
                  (i) => BookInfo.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> checknewvippe(
    String userId,
    String areaCode,
    String schoolId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'areaCode': areaCode,
      r'schoolId': schoolId,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/hk/checknewvippe',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<ClassInfoModel>>> getJoinedClazzList(
      String studentId) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'studentId': studentId};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<ClassInfoModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/clazzStudent/getJoinedClazzList',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<ClassInfoModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<ClassInfoModel>(
                  (i) => ClassInfoModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> updatePriority(String clazzId) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {'clazzId': clazzId};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/clazzStudent/updatePriority',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<ClassDetailInfoModel>> getClazzInfo(
      String clazzId) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'clazzId': clazzId};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<ClassDetailInfoModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/clazzStudent/getClazzInfo',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<ClassDetailInfoModel>.fromJson(
      _result.data!,
      (json) => ClassDetailInfoModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<NewClassInfoModel>> queryclazzbyclazznonew(
      String clazzNo) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'clazzNo': clazzNo};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<NewClassInfoModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/clazzStudent/queryclazzbyclazznonew',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<NewClassInfoModel>.fromJson(
      _result.data!,
      (json) => NewClassInfoModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> checksydata(
    List<String> contentList,
    String? test,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'contentList': contentList,
      'test': test,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/sensitivity-api/api/sensitivity/v50/sy/checksydata',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> exitClazz(
    String clazzId,
    String? studentId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'clazzId': clazzId,
      'studentId': studentId,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/clazzStudent/exitClazz',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<ClassMemberListResponseModel>> queryMemberByStudent(
    String clazzId,
    String userId,
    int pageIndex,
    int pageSize,
    String? orderBy,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{
      r'clazzId': clazzId,
      r'userId': userId,
      r'pageIndex': pageIndex,
      r'pageSize': pageSize,
      r'orderBy': orderBy,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<ClassMemberListResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/clazzStudent/queryMemberByStudent',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<ClassMemberListResponseModel>.fromJson(
      _result.data!,
      (json) =>
          ClassMemberListResponseModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> dynamicUplike(
    String userId,
    String dynamicId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'userId': userId,
      'dynamicId': dynamicId,
    };
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/datacenter-api/api/datacenter/v50/dynamic/uplike',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<RjddBookRelativeModel>>>
      queryBookRelationMap() async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<RjddBookRelativeModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/order-api/api/yyb/clicklearn/v1/queryBookRelationMap',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<RjddBookRelativeModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<RjddBookRelativeModel>((i) =>
                  RjddBookRelativeModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<BookInfoDetail>> getbook(
    String? bookId,
    String? versionNum,
    String? appId,
    String? userId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'bookId': bookId,
      r'versionNum': versionNum,
      r'appId': appId,
      r'userId': userId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<BookInfoDetail>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v51/book/getbook',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<BookInfoDetail>.fromJson(
      _result.data!,
      (json) => BookInfoDetail.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<BookInfoDetail>> onebookdata(
    String? id,
    String? versionNum,
    String? appId,
    String? userId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'id': id,
      r'versionNum': versionNum,
      r'appId': appId,
      r'userId': userId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<BookInfoDetail>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v51/book/onebookdata',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<BookInfoDetail>.fromJson(
      _result.data!,
      (json) => BookInfoDetail.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<ListenreSourceColumnModel>>> listenresourcecolumn(
      String? grade) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'grade': grade};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<ListenreSourceColumnModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/interest/v50/bookread/listenresourcecolumn',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<ListenreSourceColumnModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<ListenreSourceColumnModel>((i) =>
                  ListenreSourceColumnModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<String>> updatePoint(
    String? ruleCode,
    String? moduleId,
    String? detailId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'ruleCode': ruleCode,
      'moduleId': moduleId,
      'detailId': detailId,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<String>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v2/point/api/updatePoint',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<String>.fromJson(
      _result.data!,
      (json) => json as String,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<RecommendModel>>> queryrecommendList(
    String subject,
    String recommendPoint,
    String? bookId,
    String? grade,
    String versionNum,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'subject': subject,
      r'recommendPoint': recommendPoint,
      r'bookId': bookId,
      r'grade': grade,
      r'versionNum': versionNum,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<RecommendModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/advertise-api/api/advertise/v50/recommend/queryrecommendList',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<RecommendModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<RecommendModel>(
                  (i) => RecommendModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> readresultnew(
    String oralunitId,
    String answers,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'oralunitId': oralunitId,
      'answers': answers,
    };
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/studyrecord-api/api/oralunit/v70/info/readresultnew',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<AppExamPaperModel>> queryappexampaper(String id) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'id': id};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<AppExamPaperModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/question/v50/exampaper/queryappexampaper',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<AppExamPaperModel>.fromJson(
      _result.data!,
      (json) => AppExamPaperModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> submitexam(
    String? provinceCity,
    String? grade,
    String userId,
    String moduleId,
    String? examPaperId,
    String? examName,
    String? examTypeName,
    String? jointExamineName,
    String? tf,
    int? trueCount,
    int? falseCount,
    int? halfCount,
    num? tfRate,
    List<String> results,
    List<String> rightResults,
    int? costSecond,
    String? everyCostSecond,
    List<String> questionIds,
    List<String>? images,
    List<String>? vedios,
    List<String>? audios,
    String? dedicatedRegion,
    num? totalScore,
    num? score,
    num? completionRate,
    String? examCard,
    String id,
    String subject,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'provinceCity': provinceCity,
      'grade': grade,
      'userId': userId,
      'moduleId': moduleId,
      'examPaperId': examPaperId,
      'examName': examName,
      'examTypeName': examTypeName,
      'jointExamineName': jointExamineName,
      'tf': tf,
      'trueCount': trueCount,
      'falseCount': falseCount,
      'halfCount': halfCount,
      'tfRate': tfRate,
      'results': results,
      'rightResults': rightResults,
      'costSecond': costSecond,
      'everyCostSecond': everyCostSecond,
      'questionIds': questionIds,
      'images': images,
      'vedios': vedios,
      'audios': audios,
      'dedicatedRegion': dedicatedRegion,
      'totalScore': totalScore,
      'score': score,
      'completionRate': completionRate,
      'examCard': examCard,
      'id': id,
      'subject': subject,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/studyrecord/v50/breakthrough/submitexam',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> saveuserunitexampapercompletionprog(
    String bookId,
    String bookUnitId,
    String? paramId,
    String subId,
    String resourceId,
    String userId,
    int studyTime,
    int qusNum,
    String remark,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'bookId': bookId,
      'bookUnitId': bookUnitId,
      'paramId': paramId,
      'subId': subId,
      'resourceId': resourceId,
      'userId': userId,
      'studyTime': studyTime,
      'qusNum': qusNum,
      'remark': remark,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/studyrecord/v50/820/bookunit/saveuserunitexampapercompletionprog',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> saveuserkwpycompletionprog(
    String bookId,
    String? paramId,
    String subId,
    String userId,
    int studyTime,
    String remark,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'bookId': bookId,
      'paramId': paramId,
      'subId': subId,
      'userId': userId,
      'studyTime': studyTime,
      'remark': remark,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/studyrecord/v50/820/bookunit/saveuserkwpycompletionprog',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<QueryProgressDataModel>> queryprogressdata(
    String? id,
    String? type,
    String? subject,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'id': id,
      r'type': type,
      r'subject': subject,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<QueryProgressDataModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/studyrecord/v50/breakthrough/queryprogressdata',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<QueryProgressDataModel>.fromJson(
      _result.data!,
      (json) => QueryProgressDataModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<PartExamProgressModel>> querypartexamprogress(
    String? id,
    String? subject,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'id': id,
      r'subject': subject,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<PartExamProgressModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/studyrecord/v50/breakthrough/querypartexamprogress',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<PartExamProgressModel>.fromJson(
      _result.data!,
      (json) => PartExamProgressModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<QuestionList>>> querytodayerrorqn(
    String? userId,
    String? subject,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'subject': subject,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<QuestionList>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/questionrecord/v50/errorqn/querytodayerrorqn',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<QuestionList>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<QuestionList>(
                  (i) => QuestionList.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<QuestionList>>> querynorevisionerrorqn(
    String? userId,
    String? subject,
    String? type,
    String? bookId,
    String? unitId,
    String? qyType,
    String? labelId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'subject': subject,
      r'type': type,
      r'bookId': bookId,
      r'unitId': unitId,
      r'qyType': qyType,
      r'labelId': labelId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<QuestionList>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/questionrecord/v50/errorqn/querynorevisionerrorqn',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<QuestionList>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<QuestionList>(
                  (i) => QuestionList.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> submiterrorquestions(
    String? provinceCity,
    String? grade,
    String? userId,
    String? subject,
    List<String>? results,
    String? tf,
    List<String>? questionIds,
    String? everyCostSecond,
    List<String>? resourceIds,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'provinceCity': provinceCity,
      'grade': grade,
      'userId': userId,
      'subject': subject,
      'results': results,
      'tf': tf,
      'questionIds': questionIds,
      'everyCostSecond': everyCostSecond,
      'resourceIds': resourceIds,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/questionrecord/v50/errorqn/submiterrorquestions',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<BreakThroughLevelModel>>> querybreakthroughlevel(
    String? id,
    String? userId,
    String? moduleId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'id': id,
      r'userId': userId,
      r'moduleId': moduleId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<BreakThroughLevelModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/breakthrough/querybreakthroughlevel',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<BreakThroughLevelModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<BreakThroughLevelModel>((i) =>
                  BreakThroughLevelModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<QuestionList>>> queryapplevelquestion(
    String? id,
    String? userId,
    String? questionNum,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'id': id,
      r'userId': userId,
      r'questionNum': questionNum,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<QuestionList>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/breakthrough/queryapplevelquestion',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<QuestionList>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<QuestionList>(
                  (i) => QuestionList.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> submitpractice(
    String? provinceCity,
    String? grade,
    String userId,
    String moduleId,
    String? breakthroughUnitId,
    String? breakthroughLevelId,
    String? exerciseUnitName,
    String? gearName,
    String? levelName,
    int? trueCount,
    int? falseCount,
    int? halfCount,
    num? tfRate,
    List<String> results,
    List<String> rightResults,
    int? costSecond,
    String? everyCostSecond,
    List<String> questionIds,
    List<String>? images,
    List<String>? vedios,
    List<String>? audios,
    String? tf,
    String subject,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'provinceCity': provinceCity,
      'grade': grade,
      'userId': userId,
      'moduleId': moduleId,
      'breakthroughUnitId': breakthroughUnitId,
      'breakthroughLevelId': breakthroughLevelId,
      'exerciseUnitName': exerciseUnitName,
      'gearName': gearName,
      'levelName': levelName,
      'trueCount': trueCount,
      'falseCount': falseCount,
      'halfCount': halfCount,
      'tfRate': tfRate,
      'results': results,
      'rightResults': rightResults,
      'costSecond': costSecond,
      'everyCostSecond': everyCostSecond,
      'questionIds': questionIds,
      'images': images,
      'vedios': vedios,
      'audios': audios,
      'tf': tf,
      'subject': subject,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/studyrecord/v50/breakthrough/submitpractice',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<FindWordDataModel>> findworddata(
    String? wordName,
    String? unitId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'wordName': wordName,
      r'unitId': unitId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<FindWordDataModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/unitword/findworddata',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<FindWordDataModel>.fromJson(
      _result.data!,
      (json) => FindWordDataModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<ClickLearnTicketModel>>> queryClickLearnTicket(
    String? userId,
    String? bookId,
    String? useFlag,
    String? overdueFlag,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'bookId': bookId,
      r'useFlag': useFlag,
      r'overdueFlag': overdueFlag,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<ClickLearnTicketModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/order-api/api/yyb/clicklearn/v1/queryClickLearnTicket',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<ClickLearnTicketModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<ClickLearnTicketModel>((i) =>
                  ClickLearnTicketModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<UserWithOtherInfoForH5Model>> getUserWithOtherInfoForH5(
      String? userId) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'userId': userId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<UserWithOtherInfoForH5Model>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/user/api/getUserWithOtherInfoForH5',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<UserWithOtherInfoForH5Model>.fromJson(
      _result.data!,
      (json) =>
          UserWithOtherInfoForH5Model.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> verifyManagePassword(
    String? userId,
    String? password,
    String? enpassword,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'password': password,
      r'enpassword': enpassword,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/user/api/verifyManagePassword',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> changeManagePass(
    String? userId,
    String? password,
    String? oldPass,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'userId': userId,
      'password': password,
      'oldPass': oldPass,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/user/api/changeManagePass',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> verifyResetManagePass(
    String? userId,
    String? password,
    String? phone,
    String? verifyCode,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'userId': userId,
      'password': password,
      'phone': phone,
      'verifyCode': verifyCode,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/user/api/verifyResetManagePass',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> redeemClickLearnTicket(
    String? userId,
    String? bookId,
    String? id,
    String? phonePlatform,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'userId': userId,
      'bookId': bookId,
      'id': id,
      'phonePlatform': phonePlatform,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/order-api/api/yyb/clicklearn/v1/redeemClickLearnTicket',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<SemesterDataModel>> querysemesterdata(
    String? userId,
    String? subject,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'subject': subject,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<SemesterDataModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/homework/v50/semester/querysemesterdata',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<SemesterDataModel>.fromJson(
      _result.data!,
      (json) => SemesterDataModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> usehead(
    String? appId,
    String? headId,
    String? glory,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'appId': appId,
      'headId': headId,
      'glory': glory,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/user/v50/user/api/usehead',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> savestartthe(
    String? userId,
    String? appVersion,
    String? phoneName,
    String? phoneModel,
    String? platformName,
    String? platformVersion,
    String? osName,
    String? deviceId,
    String? userType,
    String? appId,
    String? address,
    String? source,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'userId': userId,
      'appVersion': appVersion,
      'phoneName': phoneName,
      'phoneModel': phoneModel,
      'platformName': platformName,
      'platformVersion': platformVersion,
      'osName': osName,
      'deviceId': deviceId,
      'userType': userType,
      'appId': appId,
      'address': address,
      'source': source,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/behavior-api/api/yyb/v1/startthe/save',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<DictTitleModel>>> getDictTitle(String type) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'types': type};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<DictTitleModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/appconfig-api/api/yyb/v1/dict/phone/getDictTitle',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<DictTitleModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<DictTitleModel>(
                  (i) => DictTitleModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> updateprodata(
    String? homeworkId,
    List<String>? images,
    List<String>? vedios,
    List<String>? audios,
    List<String>? results,
    String? moduleId,
    String? clazzId,
    String? id,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'homeworkId': homeworkId,
      'images': images,
      'vedios': vedios,
      'audios': audios,
      'results': results,
      'moduleId': moduleId,
      'clazzId': clazzId,
      'id': id,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/homework/v50/student/updateprodata',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> submithk(
    String? provinceCity,
    String? grade,
    String? userId,
    String? type,
    String? detailId,
    String? homeworkId,
    String? tf,
    String? everyCostSecond,
    List<String>? images,
    String? costSecond,
    List<String>? vedios,
    List<String>? audios,
    List<String>? results,
    List<String>? rightResults,
    String? moduleId,
    String? tfRate,
    String? receiveVal,
    String? academicYear,
    String? fascicule,
    List<String>? noQns,
    String? clazzId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'provinceCity': provinceCity,
      'grade': grade,
      'userId': userId,
      'type': type,
      'detailId': detailId,
      'homeworkId': homeworkId,
      'tf': tf,
      'everyCostSecond': everyCostSecond,
      'images': images,
      'costSecond': costSecond,
      'vedios': vedios,
      'audios': audios,
      'results': results,
      'rightResults': rightResults,
      'moduleId': moduleId,
      'tfRate': tfRate,
      'receiveVal': receiveVal,
      'academicYear': academicYear,
      'fascicule': fascicule,
      'noQns': noQns,
      'clazzId': clazzId,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/homework/v50/student/submithk',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> updateappstatus(
    String? auditStatus,
    String? reason,
    String? id,
    String? userId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'auditStatus': auditStatus,
      'reason': reason,
      'id': id,
      'userId': userId,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/syncquestion/updateappstatus',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> updatetfresults(
    String? dateName,
    List<String>? ids,
    List<String>? tfResults,
    String? clazzId,
    String? userId,
    String? homeworkId,
    String? homeworkType,
    List<String>? questionIds,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'dateName': dateName,
      'ids': ids,
      'tfResults': tfResults,
      'clazzId': clazzId,
      'userId': userId,
      'homeworkId': homeworkId,
      'homeworkType': homeworkType,
      'questionIds': questionIds,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/questionrecord/v50/hkrevision/updatetfresults',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> queryAllModules(String userId) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'userId': userId};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/homework-api/api/yyb/v1/commonApi/queryAllModules',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> submitunifiedtestexam(
    String? provinceCity,
    String? grade,
    String? id,
    String? examName,
    String? examPaperId,
    List<String>? results,
    String? userId,
    int? falseCount,
    int? trueCount,
    int? costSecond,
    String? everyCostSecond,
    List<String>? questionIds,
    List<String>? questionScore,
    List<String>? answerScore,
    int? tfRate,
    List<String>? rightResults,
    String? resourcesId,
    String? clazzId,
    int? halfCount,
    String? seriesName,
    String? seriesId,
    String? tf,
    String? areaCode,
    String? schoolId,
    String? examCard,
    int? totalScore,
    double? score,
    int? completionRate,
    int? examDuration,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'provinceCity': provinceCity,
      'grade': grade,
      'id': id,
      'examName': examName,
      'examPaperId': examPaperId,
      'results': results,
      'userId': userId,
      'falseCount': falseCount,
      'trueCount': trueCount,
      'costSecond': costSecond,
      'everyCostSecond': everyCostSecond,
      'questionIds': questionIds,
      'questionScore': questionScore,
      'answerScore': answerScore,
      'tfRate': tfRate,
      'rightResults': rightResults,
      'resourcesId': resourcesId,
      'clazzId': clazzId,
      'halfCount': halfCount,
      'seriesName': seriesName,
      'seriesId': seriesId,
      'tf': tf,
      'areaCode': areaCode,
      'schoolId': schoolId,
      'examCard': examCard,
      'totalScore': totalScore,
      'score': score,
      'completionRate': completionRate,
      'examDuration': examDuration,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/activity-api/api/activity/v50/unifiedtestsum/submitunifiedtestexam',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<StatisticsTestSubmintItem>> querypartunifiedtestprogress(
      String? id) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'id': id};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<StatisticsTestSubmintItem>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/activity-api/api/activity/v50/unifiedtestsum/querypartunifiedtestprogress',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<StatisticsTestSubmintItem>.fromJson(
      _result.data!,
      (json) =>
          StatisticsTestSubmintItem.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<ActivityQuestionResponseModel>> queryQuestions(
    String? activityId,
    String? ids,
    String? wordIds,
    String? versionId,
    String? userId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'activityId': activityId,
      r'ids': ids,
      r'wordIds': wordIds,
      r'versionId': versionId,
      r'userId': userId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<ActivityQuestionResponseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/activity-api/api/yyb/v1/activity/api/queryQuestions',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<ActivityQuestionResponseModel>.fromJson(
      _result.data!,
      (json) =>
          ActivityQuestionResponseModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<SubmitAnswerInfoModel>> submitAnsersInfo(
    String? activityId,
    String? answers,
    String? totalQuesCount,
    String? successQuesCount,
    String? rightRate,
    String? spendSeconds,
    String? startTime,
    String? endTime,
    String? faildQuesCount,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'activityId': activityId,
      'answers': answers,
      'totalQuesCount': totalQuesCount,
      'successQuesCount': successQuesCount,
      'rightRate': rightRate,
      'spendSeconds': spendSeconds,
      'startTime': startTime,
      'endTime': endTime,
      'faildQuesCount': faildQuesCount,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<SubmitAnswerInfoModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/activity-api/api/yyb/v1/activity/api/submitAnsersInfo',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<SubmitAnswerInfoModel>.fromJson(
      _result.data!,
      (json) => SubmitAnswerInfoModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> saveErrorWord(
    List<ErrorWordBean>? errorWords,
    String? test,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'errorWords': errorWords,
      'test': test,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/word-api/api/yyb/v1/qiaoxue/phone/saveErrorWord',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> addpubquestionpro(
    String? provinceCity,
    String? grade,
    String? userId,
    String? subject,
    List<String>? results,
    String? tf,
    List<String>? questionIds,
    String? everyCostSecond,
    String? moduleId,
    String? sourceType,
    String? specificType,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'provinceCity': provinceCity,
      'grade': grade,
      'userId': userId,
      'subject': subject,
      'results': results,
      'tf': tf,
      'questionIds': questionIds,
      'everyCostSecond': everyCostSecond,
      'moduleId': moduleId,
      'sourceType': sourceType,
      'specificType': specificType,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/questionrecord/v50/errorqn/addpubquestionpro',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> getMyBestResult(
    String? userId,
    String? activityId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'activityId': activityId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/activity-api/api/yyb/v1/activity/api/getMyBestResult',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<String> domain() async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
      'host_name': 'domainhost',
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<String>(_setStreamType<String>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/domain',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = _result.data!;
    return value;
  }

  @override
  Future<BaseResponse<StatisticsTestSubmintItem>> querypartoralmtestpro(
    String? id,
    String? subject,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'id': id,
      r'subject': subject,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<StatisticsTestSubmintItem>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/breakthrough/querypartoralmtestpro',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<StatisticsTestSubmintItem>.fromJson(
      _result.data!,
      (json) =>
          StatisticsTestSubmintItem.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> submitoralmtest(
    String? userId,
    String? moduleId,
    String? examPaperId,
    String? examName,
    String? examTypeName,
    String? jointExamineName,
    String? tf,
    int? falseCount,
    int? trueCount,
    int? halfCount,
    num? tfRate,
    List<String>? results,
    List<String>? rightResults,
    int? costSecond,
    String? everyCostSecond,
    List<String>? questionIds,
    String? dedicatedRegion,
    num? totalScore,
    num? score,
    num? completionRate,
    String? examCard,
    String? id,
    String? subject,
    List<String>? questionScore,
    int? examDuration,
    List<String>? answerScore,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'userId': userId,
      'moduleId': moduleId,
      'examPaperId': examPaperId,
      'examName': examName,
      'examTypeName': examTypeName,
      'jointExamineName': jointExamineName,
      'tf': tf,
      'falseCount': falseCount,
      'trueCount': trueCount,
      'halfCount': halfCount,
      'tfRate': tfRate,
      'results': results,
      'rightResults': rightResults,
      'costSecond': costSecond,
      'everyCostSecond': everyCostSecond,
      'questionIds': questionIds,
      'dedicatedRegion': dedicatedRegion,
      'totalScore': totalScore,
      'score': score,
      'completionRate': completionRate,
      'examCard': examCard,
      'id': id,
      'subject': subject,
      'questionScore': questionScore,
      'examDuration': examDuration,
      'answerScore': answerScore,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/studyrecord/v50/breakthrough/submitoralmtest',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<String>> findindependenttype(String? type) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'type': type};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<String>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/independent/findindependenttype',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<String>.fromJson(
      _result.data!,
      (json) => json as String,
    );
    return value;
  }

  @override
  Future<BaseResponse<QcCodeAudioInfo>> queryQRCodeAudio(
    String? type,
    String? id,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'type': type,
      r'id': id,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<QcCodeAudioInfo>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/scan',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<QcCodeAudioInfo>.fromJson(
      _result.data!,
      (json) => QcCodeAudioInfo.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<QuestionList>>> selectquestionbypractice(
      String? practiceId) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'practiceId': practiceId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<QuestionList>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/manmachinetalkapi/selectquestionbypractice',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<QuestionList>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<QuestionList>(
                  (i) => QuestionList.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<PracticeReportModel>> selectpracticereport(
    String? breakthroughId,
    String? unitId,
    String? userId,
    String? practiceId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'breakthroughId': breakthroughId,
      r'unitId': unitId,
      r'userId': userId,
      r'practiceId': practiceId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<PracticeReportModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/manmachinetalkapi/selectpracticereport',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<PracticeReportModel>.fromJson(
      _result.data!,
      (json) => PracticeReportModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<ResultInfoBean>> submitpracticeinfo(
    String? breakthroughId,
    String? breakthroughExerciseUnitId,
    String? practiceId,
    List<SubmitPracticeInfoBean>? recordList,
    num? completeRate,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'breakthroughId': breakthroughId,
      'breakthroughExerciseUnitId': breakthroughExerciseUnitId,
      'practiceId': practiceId,
      'recordList': recordList,
      'completeRate': completeRate,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<ResultInfoBean>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/manmachinetalkapi/submitpracticeinfo',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<ResultInfoBean>.fromJson(
      _result.data!,
      (json) => ResultInfoBean.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<LearnCourseVideoInfo>> videooptions(
      String? gmManageId) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'gmManageId': gmManageId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<LearnCourseVideoInfo>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/word/v50/train/camp/course/videooptions',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<LearnCourseVideoInfo>.fromJson(
      _result.data!,
      (json) => LearnCourseVideoInfo.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<CommonPointResponseModel>> submitwatch(
    String? trainingCampId,
    String? tcManageId,
    String? manageId,
    String? userId,
    int? watchSpeed,
    int? watchTime,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'trainingCampId': trainingCampId,
      'tcManageId': tcManageId,
      'manageId': manageId,
      'userId': userId,
      'watchSpeed': watchSpeed,
      'watchTime': watchTime,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<CommonPointResponseModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/studyrecord/v50/train/camp/course/submitwatch',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<CommonPointResponseModel>.fromJson(
      _result.data!,
      (json) => CommonPointResponseModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> addSentenceRecord(
    String? contentId,
    String? content,
    String? type,
    String? bookId,
    String? remarks,
    String? referenceId,
    String? requestId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': false,
      'host_name': 'loghost',
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'contentId': contentId,
      'content': content,
      'type': type,
      'bookId': bookId,
      'remarks': remarks,
      'referenceId': referenceId,
      'requestId': requestId,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/behavior-api/api/yyb/v1/record/api/addSentenceRecord',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<int>> sign(String userId) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {'userId': userId};
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<BaseResponse<int>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/sign/api/sign',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<int>.fromJson(
      _result.data!,
      (json) => json as int,
    );
    return value;
  }

  @override
  Future<BaseResponse<MathVideoBean>> selectvideoresourcelist(
    String bookId,
    String oneUnitId,
    String userId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'bookId': bookId,
      r'oneUnitId': oneUnitId,
      r'userId': userId,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<MathVideoBean>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/math/v50/mathknowledgecontrollerapi/selectvideoresourcelist',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<MathVideoBean>.fromJson(
      _result.data!,
      (json) => MathVideoBean.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<MathDictListBean>>> selectunitlistbybook(
      String? bookId) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'bookId': bookId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<MathDictListBean>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/mathknowledgeapicontroller/selectunitlistbybook',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<MathDictListBean>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<MathDictListBean>(
                  (i) => MathDictListBean.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<CityHighErrorQnBean>> querycityhigherrorqn(
    String? resourceId,
    String? userId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'resourceId': resourceId,
      r'userId': userId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<CityHighErrorQnBean>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/questionrecord/v50/errorqn/querycityhigherrorqn',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<CityHighErrorQnBean>.fromJson(
      _result.data!,
      (json) => CityHighErrorQnBean.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> submithighqnpro(
    String? provinceCity,
    String? grade,
    String? userId,
    String? subject,
    String? resourceId,
    String? tfRate,
    List<String>? results,
    List<String>? rightResults,
    String? tf,
    num? costSecond,
    List<String>? questionIds,
    String? everyCostSecond,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'provinceCity': provinceCity,
      'grade': grade,
      'userId': userId,
      'subject': subject,
      'resourceId': resourceId,
      'tfRate': tfRate,
      'results': results,
      'rightResults': rightResults,
      'tf': tf,
      'costSecond': costSecond,
      'questionIds': questionIds,
      'everyCostSecond': everyCostSecond,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/questionrecord/v50/errorqn/submithighqnpro',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> submitweekqnpro(
    String? provinceCity,
    String? grade,
    String? userId,
    String? subject,
    String? resourceId,
    String? tfRate,
    List<String>? results,
    List<String>? rightResults,
    String? tf,
    num? costSecond,
    List<String>? questionIds,
    String? everyCostSecond,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'provinceCity': provinceCity,
      'grade': grade,
      'userId': userId,
      'subject': subject,
      'resourceId': resourceId,
      'tfRate': tfRate,
      'results': results,
      'rightResults': rightResults,
      'tf': tf,
      'costSecond': costSecond,
      'questionIds': questionIds,
      'everyCostSecond': everyCostSecond,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/questionrecord/v50/errorqn/submitweekqnpro',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<QuestionList>>> queryrevisionerrorqn(
    String? subject,
    String? userId,
    List<String>? strList,
    List<String>? questionIds,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'subject': subject,
      'userId': userId,
      'strList': strList,
      'questionIds': questionIds,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<QuestionList>>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/questionrecord/v50/errorqn/queryrevisionerrorqn',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<QuestionList>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<QuestionList>(
                  (i) => QuestionList.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<CityHighErrorQnBean>> queryweekqn(
    String? userId,
    String? id,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'id': id,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<CityHighErrorQnBean>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/questionrecord/v50/errorqn/queryweekqn',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<CityHighErrorQnBean>.fromJson(
      _result.data!,
      (json) => CityHighErrorQnBean.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }


  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }
}
