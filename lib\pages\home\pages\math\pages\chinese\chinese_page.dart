// import 'package:flutter/cupertino.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/config/app_config.dart';
// import 'package:lib_base/model/http/ad_info.dart';
// import 'package:lib_base/model/http/book_study_record_vo.dart';
// import 'package:lib_base/utils/screen_util.dart';
// import 'package:lib_base/widgets/refresh/refresh_view.dart';
// import 'package:lib_base/model/http/book_info_detail.dart';
// import 'package:yyb_flutter/model/http/query_chinese_expand_model.dart';
// import 'package:yyb_flutter/pages/home/<USER>/widget/home_ad_banner.dart';
// import 'package:lib_base/widgets/business/title_row/has_more_title_row.dart';
// import 'package:yyb_flutter/pages/home/<USER>/math/pages/chinese/widgets/book_and_unit_switch.dart';
// import 'package:yyb_flutter/pages/home/<USER>/math/pages/chinese/widgets/column_item.dart';
// import 'package:yyb_flutter/pages/home/<USER>/math/pages/chinese/widgets/expand_model_obj_item_widget.dart';
// import 'package:yyb_flutter/pages/home/<USER>/math/pages/chinese/widgets/expand_model_record_item_widget.dart';
// import 'package:yyb_flutter/pages/home/<USER>/math/pages/chinese/widgets/study_module_widget.dart';
// import 'package:lib_base/providers/books/english/english_book_info_provider.dart';
// import 'package:yyb_flutter/providers/chinese_math/chinese/chinese_page_provider.dart';

// //ChineseHomeFragment
// class ChinesePage extends ConsumerWidget {
//   const ChinesePage({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     var controller = ref.watch(chinesePageNotifierProvider.notifier);
//     return RefreshView(
//         sliversBuilder: (_) {
//           List<AdInfo> courseBannerAds = controller.getPageBannerAds();
//           MMBookInfo? bookInfo = controller.bookInfo;
//           BookInfoDetail? bookInfoDetail = controller.bookInfoDetail;
//           List<QueryChineseExpandModel>? queryChineseExpandModelList =
//               controller.queryChineseExpandModelList;
//           return [
//             SliverToBoxAdapter(
//               child: HomeAdBanner(
//                 ads: courseBannerAds,
//               ),
//             ),
//             SliverToBoxAdapter(
//               child: AppConfig.isNeedHideForUnfinished
//                   ? const SizedBox()
//                   : BookAndUnitSwitchCard(
//                       bookInfo: bookInfo,
//                       bookInfoDetail: bookInfoDetail,
//                     ),
//             ),
//             ..._studyModuleCard(bookInfoDetail, controller),

//             ...AppConfig.isNeedHideForUnfinished
//                 ? []
//                 : _columnListCard(bookInfoDetail),
//             // //
//             ...AppConfig.isNeedHideForUnfinished
//                 ? []
//                 : _queryChineseExpandModelListWidgets(
//                     queryChineseExpandModelList ?? [])
//           ];
//         },
//         controller: controller);
//   }

//   List<Widget> _studyModuleCard(
//       BookInfoDetail? bookInfoDetail, ChinesePageNotifier controller) {
//     List<BookModuleInfoList> list =
//         bookInfoDetail?.bookStudyRecordVO?.bookModuleInfoList ?? [];
//     double childAspectRatio = MScreenUtil.isPad()
//         ? 56 / 40
//         : MScreenUtil.isFoldable()
//             ? 56 / 69
//             : 56 / 69;
//     //添加内容
//     int crossAxisCount = MScreenUtil.isPad()
//         ? 6
//         : MScreenUtil.isFoldable()
//             ? 5
//             : 4;

   

//     if (list.isNotEmpty) {
//       return [
//         SliverToBoxAdapter(
//           child: SizedBox(
//             height: 20.h,
//           ),
//         ),
//         SliverGrid(
//             delegate: SliverChildBuilderDelegate((_, index) {
//               var item = list[index];
//               return StudyModuleItemWidget(
//                 module: item,
//                 onTap: () {
//                   controller.toModule(item);
//                 },
//               );
//             }, childCount: list.length),
//             gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                 crossAxisCount: crossAxisCount,
//                 childAspectRatio: childAspectRatio,
//                 crossAxisSpacing: 10))
//       ];
//     } else {
//       return [];
//     }
//   }

//   //阅读写作
//   List<Widget> _columnListCard(BookInfoDetail? bookInfoDetail) {
//     List<BookColumnList> columnList = bookInfoDetail?.bookColumnList ?? [];
//     if (columnList.isNotEmpty) {
//       List<Widget> result = [];
//       for (BookColumnList column in columnList) {
//         String type = column.type ?? "";
//         bool isNew = column.newFlag == "1";
//         List<BookModuleInfoList> bookModuleInfoList =
//             column.bookModuleInfoList ?? [];
//         if (bookModuleInfoList.isNotEmpty) {
//           //标题
//           result.add(SliverToBoxAdapter(
//             child: Padding(
//               padding: EdgeInsets.only(top: 25.h, bottom: 15.h),
//               child: HasMoreTitleRow(
//                 name: column.name ?? "",
//                 isMore: false,
//                 isNew: isNew,
//               ),
//             ),
//           ));
//           if (type == "1") {
//             result.add(SliverGrid(
//                 delegate: SliverChildBuilderDelegate((_, index) {
//                   var item = bookModuleInfoList[index];
//                   return ColumnItemType1(item: item);
//                 }, childCount: bookModuleInfoList.length),
//                 gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                     crossAxisCount: 4,
//                     childAspectRatio: 56 / 59,
//                     crossAxisSpacing: 10)));
//           } else if (type == "2") {
//             result.add(SliverGrid(
//                 delegate: SliverChildBuilderDelegate((_, index) {
//                   var item = bookModuleInfoList[index];
//                   return ColumnItemType2(item: item);
//                 }, childCount: bookModuleInfoList.length),
//                 gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                     crossAxisCount: 2,
//                     childAspectRatio: 165.5 / 54,
//                     crossAxisSpacing: 10,
//                     mainAxisSpacing: 10)));
//           } else if (type == "3") {
//             result.add(SliverList(
//                 delegate: SliverChildBuilderDelegate((_, index) {
//               BookModuleInfoList item = bookModuleInfoList[index];
//               return Padding(
//                 padding: EdgeInsets.only(top: 8.0),
//                 child: ColumnItemType3(item: item),
//               );
//             }, childCount: bookModuleInfoList.length)));
//           } else {
//             result.add(SliverGrid(
//                 delegate: SliverChildBuilderDelegate((_, index) {
//                   BookModuleInfoList item = bookModuleInfoList[index];
//                   return ColumnItemDefault(item: item);
//                 }, childCount: bookModuleInfoList.length),
//                 gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                     crossAxisCount: 3,
//                     childAspectRatio: 56 / 59,
//                     crossAxisSpacing: 10)));
//           }
//         }
//       }
//       return result;
//     } else {
//       return [];
//     }
//   }

//   List<Widget> _queryChineseExpandModelListWidgets(
//       List<QueryChineseExpandModel> queryChineseExpandModelList) {
//     if (queryChineseExpandModelList.isNotEmpty) {
//       List<Widget> result = [];
//       for (QueryChineseExpandModel model in queryChineseExpandModelList) {
//         String typeStr = model.typeStr ?? "";
//         List<Obj> objs = model.obj ?? [];
//         List<RecordedType> recordTypes = model.recordedType ?? [];
//         bool isMore = model.isMore ?? false;
//         if (objs.isNotEmpty) {
//           //标题
//           result.add(SliverToBoxAdapter(
//             child: Padding(
//               padding: EdgeInsets.only(top: 25.h, bottom: 15.h),
//               child: HasMoreTitleRow(
//                 name: model.name ?? "",
//                 isMore: isMore,
//               ),
//             ),
//           ));
//           //内容
//           result.add(SliverToBoxAdapter(
//             child: ExpandModelCard(
//               objs: objs,
//             ),
//           ));
//         } else if (recordTypes.isNotEmpty) {
//           //标题
//           result.add(SliverToBoxAdapter(
//             child: Padding(
//               padding: EdgeInsets.only(top: 25.h, bottom: 15.h),
//               child: HasMoreTitleRow(
//                 name: model.name ?? "",
//                 isMore: isMore,
//               ),
//             ),
//           ));
//           for (RecordedType type in recordTypes) {
//             result.add(
//                 SliverToBoxAdapter(child: ExpandModelRecordCard(type: type)));
//           }
//         }
//       }
//       return result;
//     } else {
//       return [];
//     }
//   }
// }
