// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_api.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers

class _AppApi implements AppApi {
  _AppApi(
    this._dio, {
    this.baseUrl,
  });

  final Dio _dio;

  String? baseUrl;

  @override
  Future<BaseResponse<SysConfig>> findByAppId(
    String version,
    String memberId,
    String appId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': true,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'version': version,
      r'memberId': memberId,
      r'appId': appId,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<SysConfig>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/appconfig-api/api/yyb/v1/set/phone/findByAppId',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<SysConfig>.fromJson(
      _result.data!,
      (json) => SysConfig.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<AdInfo>>> listavailables(
    List<String> positionIds,
    String? userLevelCode,
    String? appInnerVersion,
    String? userId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'positionIds': positionIds,
      r'userLevelCode': userLevelCode,
      r'appInnerVersion': appInnerVersion,
      r'userId': userId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<AdInfo>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/advertise-api/api/yyb/v1/advertise/info/api/listavailables',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<AdInfo>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<AdInfo>((i) => AdInfo.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<EnglishCourseInfo>>> queryEnglishCourse(
    String? grade,
    String? userLevel,
    String? appMenu,
    String? appId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'grade': grade,
      r'userLevel': userLevel,
      r'appMenu': appMenu,
      r'appId': appId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<EnglishCourseInfo>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/period/phone/queryEnglishCourse',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<EnglishCourseInfo>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<EnglishCourseInfo>(
                  (i) => EnglishCourseInfo.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<EnglishInterestInfo>>> queryenglishinterestthree(
    String? grade,
    String? userLevel,
    String? appMenu,
    String? appId,
    String? userId,
    String? versionNum,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'grade': grade,
      r'userLevel': userLevel,
      r'appMenu': appMenu,
      r'appId': appId,
      r'userId': userId,
      r'versionNum': versionNum,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<EnglishInterestInfo>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/interest/v50/dubbing/queryenglishinterestthree',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<EnglishInterestInfo>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<EnglishInterestInfo>((i) =>
                  EnglishInterestInfo.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<EnglishShortVideoInfo>> pageappshortvideodata(
    int? qryType,
    String? userId,
    int? pageNo,
    int? pageSize,
    String? name,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'qryType': qryType,
      r'userId': userId,
      r'pageNo': pageNo,
      r'pageSize': pageSize,
      r'name': name,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<EnglishShortVideoInfo>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/course/v50/shortvideo/pageappshortvideodata',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<EnglishShortVideoInfo>.fromJson(
      _result.data!,
      (json) => EnglishShortVideoInfo.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<EnglishRecommendActivity>> getRecommendActivity() async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<EnglishRecommendActivity>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/activity-api/api/yyb/v1/activity/api/getRecommendActivity',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<EnglishRecommendActivity>.fromJson(
      _result.data!,
      (json) => EnglishRecommendActivity.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<IdNameInfo>>> selectshortvideoallseries() async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<IdNameInfo>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/course/v50/shortvideo/selectshortvideoallseries',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<IdNameInfo>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<IdNameInfo>(
                  (i) => IdNameInfo.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<QueryChineseExpandModel>>> querychineseexpand(
    String? grade,
    String? userLevel,
    String? appMenu,
    String? appId,
    String? bookId,
    String? versionNum,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'grade': grade,
      r'userLevel': userLevel,
      r'appMenu': appMenu,
      r'appId': appId,
      r'bookId': bookId,
      r'versionNum': versionNum,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<QueryChineseExpandModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/period/phone/querychineseexpand',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<QueryChineseExpandModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<QueryChineseExpandModel>((i) =>
                  QueryChineseExpandModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> getMyBestResult(
    String? userId,
    String? activityId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'activityId': activityId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/activity-api/api/yyb/v1/activity/api/getMyBestResult',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<AreaSchoolModel>>> queryAreaSchool(
      String? areaNames) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'areaNames': areaNames};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<AreaSchoolModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/school-api/api/yyb/v1/schoolApi/queryAreaSchool',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<AreaSchoolModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<AreaSchoolModel>(
                  (i) => AreaSchoolModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<BookVersionModel>>> listbkversion(
    String? subject,
    String? version,
    String? grade,
    String? fascicule,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'subject': subject,
      r'version': version,
      r'grade': grade,
      r'fascicule': fascicule,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<BookVersionModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/book/listbkversion',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<BookVersionModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<BookVersionModel>(
                  (i) => BookVersionModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<BookInfoByAreaModel>>> querybooknominatebyarea(
    String? subject,
    String? areaCodes,
    String? grade,
    String? fascicule,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'subject': subject,
      r'areaCodes': areaCodes,
      r'grade': grade,
      r'fascicule': fascicule,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<BookInfoByAreaModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/school/v50/areabook/querybooknominatebyarea',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<BookInfoByAreaModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<BookInfoByAreaModel>((i) =>
                  BookInfoByAreaModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<ExplainPhoneModel>> explainPhone(String? type) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'type': type};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<ExplainPhoneModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/appconfig-api/api/yyb/v1/explain/phone',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<ExplainPhoneModel>.fromJson(
      _result.data!,
      (json) => ExplainPhoneModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> addadvice(
    String puserId,
    int issuevalue,
    String content,
    String? filePath,
    String? fileType,
    String? userPhone,
    String? uinitId,
    String? unitName,
    String? type,
    String? moduleId,
    String? moduleName,
    String? appId,
    String? subject,
    String? segment,
    String? version,
    String? grade,
    String? fascicule,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'puserId': puserId,
      'value': issuevalue,
      'content': content,
      'filePath': filePath,
      'fileType': fileType,
      'userPhone': userPhone,
      'uinitId': uinitId,
      'unitName': unitName,
      'type': type,
      'moduleId': moduleId,
      'moduleName': moduleName,
      'appId': appId,
      'subject': subject,
      'segment': segment,
      'version': version,
      'grade': grade,
      'fascicule': fascicule,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/comments/v50/advice/addadvice',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<BasePageListResponseModel<FeedbackItemModel>>>
      finduseradvices(
    String puserId,
    String? moduleId,
    int pageNo,
    int pageSize,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'puserId': puserId,
      r'moduleId': moduleId,
      r'pageNo': pageNo,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(_setStreamType<
        BaseResponse<BasePageListResponseModel<FeedbackItemModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/comments/v50/advice/finduseradvices',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value =
        BaseResponse<BasePageListResponseModel<FeedbackItemModel>>.fromJson(
      _result.data!,
      (json) => BasePageListResponseModel<FeedbackItemModel>.fromJson(
        json as Map<String, dynamic>,
        (json) => FeedbackItemModel.fromJson(json as Map<String, dynamic>),
      ),
    );
    return value;
  }

  @override
  Future<BaseResponse<UpdatePwdModel>> changePass(
    String oldPass,
    String password,
    String resPassword,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'oldPass': oldPass,
      'password': password,
      'resPassword': resPassword,
    };
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<UpdatePwdModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/user/api/changePass',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<UpdatePwdModel>.fromJson(
      _result.data!,
      (json) => UpdatePwdModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> verifyNewPhone(
    String phone,
    String verifyCode,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'phone': phone,
      'verifyCode': verifyCode,
    };
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/user/api/verifyNewPhone',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> verifyOldPhone(
    String phone,
    String verifyCode,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'phone': phone,
      'verifyCode': verifyCode,
    };
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/user/api/verifyOldPhone',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> close(String password) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {'password': password};
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/login/api/close',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<DiscoverDataModel>>> discovereddata(
    String userId,
    String grade,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'grade': grade,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<DiscoverDataModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/word-api/api/word/v50/campd/discovereddata',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<DiscoverDataModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<DiscoverDataModel>(
                  (i) => DiscoverDataModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> checkuserblend(
    String userId,
    String grade,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'blendId': grade,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/aispeak/checkuserblend',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<DisCoverItem>>> listcampdata(String grade) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'grade': grade};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<DisCoverItem>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/word-api/api/word/v50/campd/listcampdata',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<DisCoverItem>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<DisCoverItem>(
                  (i) => DisCoverItem.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> applyjoinclazzstudentnumber(
    String studentName,
    String clazzNo,
    String? studentNumber,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'studentName': studentName,
      'clazzNo': clazzNo,
      'studentNumber': studentNumber,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/clazzStudent/applyjoinclazzstudentnumber',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<MyHeaderModel>>> listmyhead(String? photo) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'photo': photo};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<MyHeaderModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/user/v50/user/api/listmyhead',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<MyHeaderModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<MyHeaderModel>(
                  (i) => MyHeaderModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<HeadInfoModel>> getheadinfo(
    String? photo,
    String? level,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'photo': photo,
      r'level': level,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<HeadInfoModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/user/v50/user/api/getheadinfo',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<HeadInfoModel>.fromJson(
      _result.data!,
      (json) => HeadInfoModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<GloryHeaderModel>>> gloryList(String? photo) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'photo': photo};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<GloryHeaderModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/user/v50/glory/list',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<GloryHeaderModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<GloryHeaderModel>(
                  (i) => GloryHeaderModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> usehead(
    String? appId,
    String? headId,
    String? glory,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'appId': appId,
      'headId': headId,
      'glory': glory,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/user/v50/user/api/usehead',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> redemptionhead(
    String? appId,
    String? headId,
    String? level,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'appId': appId,
      'headId': headId,
      'level': level,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/user/v50/user/api/redemptionhead',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<List<OrderCampandjuModel>>> listordercampandju(
      String userId) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'userId': userId};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<OrderCampandjuModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/word-api/api/word/v50/campd/listordercampandju',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<OrderCampandjuModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<OrderCampandjuModel>((i) =>
                  OrderCampandjuModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> upclazzstudentnum(
    String userId,
    String clazzId,
    String isIdentity,
    int? studentNumber,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'userId': userId,
      'clazzId': clazzId,
      'isIdentity': isIdentity,
      'studentNumber': studentNumber,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/clazzStudent/upclazzstudentnum',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<StudentDynamicModel>> getstudentdynamic(
    String userId,
    String clazzId,
    int pageNo,
    int pageSize,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'clazzId': clazzId,
      r'pageNo': pageNo,
      r'pageSize': pageSize,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<StudentDynamicModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/datacenter-api/api/datacenter/v50/dynamic/getstudentdynamic',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<StudentDynamicModel>.fromJson(
      _result.data!,
      (json) => StudentDynamicModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<ClassDynamicResponse>> getclazzstudentdynamic(
    String userId,
    String clazzId,
    int pageNo,
    int pageSize,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'clazzId': clazzId,
      r'pageNo': pageNo,
      r'pageSize': pageSize,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<ClassDynamicResponse>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/datacenter-api/api/datacenter/v50/dynamic/getclazzstudentdynamic',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<ClassDynamicResponse>.fromJson(
      _result.data!,
      (json) => ClassDynamicResponse.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<ExampleGetExampleResponse>>> exampleGetexample(
    String province,
    String city,
    String address,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'province': province,
      r'city': city,
      r'address': address,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<ExampleGetExampleResponse>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/user-api/api/yyb/v1/example/getexample',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<ExampleGetExampleResponse>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<ExampleGetExampleResponse>((i) =>
                  ExampleGetExampleResponse.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<IdNameInfo>>> queryintgunitlist(
      String bookId) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'bookId': bookId};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<IdNameInfo>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/textbook-api/api/textbook/v50/english/unit/intg/queryintgunitlist',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<IdNameInfo>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<IdNameInfo>(
                  (i) => IdNameInfo.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<BasePageListResponseModel<NewsUserReceiveModel>>>
      queryNewsUserReceive(
    int pageNo,
    int pageSize,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNo,
      r'pageSize': pageSize,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(_setStreamType<
        BaseResponse<BasePageListResponseModel<NewsUserReceiveModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/yyb/news/v3_6/queryNewsUserReceive',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value =
        BaseResponse<BasePageListResponseModel<NewsUserReceiveModel>>.fromJson(
      _result.data!,
      (json) => BasePageListResponseModel<NewsUserReceiveModel>.fromJson(
        json as Map<String, dynamic>,
        (json) => NewsUserReceiveModel.fromJson(json as Map<String, dynamic>),
      ),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> deleteNewsUserReceive(
    List<String> ids,
    String? newId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'newsUserReceiveIds': ids,
      'text': newId,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/news/v3_6/deleteNewsUserReceive',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> updateReadStatus(
    List<String> ids,
    String? newId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = {
      'newsUserReceiveIds': ids,
      'text': newId,
    };
    _data.removeWhere((k, v) => v == null);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/news/v3_6/updateReadStatus',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<ChineseModel>> onecmbookdata(
    String id,
    String versionNum,
    String userId,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{
      r'id': id,
      r'versionNum': versionNum,
      r'userId': userId,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<ChineseModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v51/book/onecmbookdata',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<ChineseModel>.fromJson(
      _result.data!,
      (json) => ChineseModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<ReadModel>>> listexrdresource(String grade) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{r'grade': grade};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<ReadModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/interest/v50/dubbing/listexrdresource',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<ReadModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<ReadModel>(
                  (i) => ReadModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<VideoModel>> onelateststvideo() async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<VideoModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/course/v50/shortvideo/onelateststvideo',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<VideoModel>.fromJson(
      _result.data!,
      (json) => VideoModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> checkstudentgroupbuy(
    String userId,
    String memberId,
    String areaCodes,
    String enVersion,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': false,
    };
    final queryParameters = <String, dynamic>{
      r'userId': userId,
      r'memberId': memberId,
      r'areaCodes': areaCodes,
      r'enVersion': enVersion,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/shop/v50/groupbuy/checkstudentgroupbuy',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<GetSignConfigModel>> getsignconfig() async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<GetSignConfigModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/user/v50/sign/getsignconfig',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<GetSignConfigModel>.fromJson(
      _result.data!,
      (json) => GetSignConfigModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  
  @override
  Future<BaseResponse<BasePageListResponseModel<ExpandVideoItem>>>
      expGetUserCollectResource(
    String? puserId,
    int? pageSize,
    int? pageIndex,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'puserId': puserId,
      r'pageSize': pageSize,
      r'pageIndex': pageIndex,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(_setStreamType<
        BaseResponse<BasePageListResponseModel<ExpandVideoItem>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/studymodules-api/api/yyb/v1/expModule/api/getUserCollectResource',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value =
        BaseResponse<BasePageListResponseModel<ExpandVideoItem>>.fromJson(
      _result.data!,
      (json) => BasePageListResponseModel<ExpandVideoItem>.fromJson(
        json as Map<String, dynamic>,
        (json) => ExpandVideoItem.fromJson(json as Map<String, dynamic>),
      ),
    );
    return value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }
}
