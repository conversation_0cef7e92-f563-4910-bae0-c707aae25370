// import 'package:flutter/cupertino.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/config/route_utils.dart';
// import 'package:lib_base/log/log.dart';
// import 'package:lib_base/utils/ui_util.dart';
// import 'package:lib_base/utils/wx_util.dart';
// import 'package:lib_base/widgets/refresh/refresh_list_view.dart';
// import 'package:yyb_flutter/common/route_define.dart';
// import 'package:yyb_flutter/pages/home/<USER>/english/pages/short_video/pages/short_video_play_page.dart';
// import 'package:yyb_flutter/pages/home/<USER>/english/pages/short_video/widgets/short_video_item.dart';
// import 'package:yyb_flutter/pages/home/<USER>/english/pages/short_video/widgets/short_video_series_widget.dart';
// import 'package:yyb_flutter/providers/english/english_short_video_page_provider.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:lib_base/utils/screen_util.dart';

// class ShortVideoPage extends ConsumerWidget {
//   const ShortVideoPage({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     EnglishShortVideoPageNotifier controller =
//         ref.watch(englishShortVideoPageNotifierProvider.notifier);
//     double aspectRatio = MScreenUtil.isPad()
//         ? 150 / 380
//         : MScreenUtil.isFoldable()
//             ? 150 / 350
//             : 150 / 320;
//     int crossAxisCount = MScreenUtil.isPad()
//         ? 4
//         : MScreenUtil.isFoldable()
//             ? 3
//             : 2;
//     double spacing = 20;
//     return RefreshListView(
//       controller: controller,
//       itemBuilder: (BuildContext context, item) {
//         return LayoutBuilder(builder: (context, constraint) {
//           double width = constraint.maxWidth;
//           return ShortVideoItem(
//             obj: item,
//             width: width,
//             onTap: () {
//               if (item.isWechat == "1") {
//                 //微信
//                 // 打开微信小程序
//                 String path =
//                     "/pages/entrance/entrance?source=channelsActivity&finderUserName=${item.videoNumId}&feedId=${item.videoId}&feedType=0";
//                 WxUtil.openMiniWx(path: path);
//               } else {
//                 String videoUrl = item.videoUrl ?? "";
//                 if (videoUrl.isNotEmpty) {
//                   //数据要滤掉微信视频， 因为他是要用小程序来播放
//                   var filtedDataList = controller.dataList.value
//                       .where((element) => element.isWechat != "1")
//                       .toList();

//                   toPage(ERouteName.shortVideoPlayer,
//                       extra: ShortVideoPageParam(
//                           currentIndex: filtedDataList.indexOf(item),
//                           objs: filtedDataList,
//                           keywords: controller.searchKey));
//                 }
//               }
//             },
//           );
//         });
//       },
//       gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//           crossAxisCount: crossAxisCount,
//           childAspectRatio: aspectRatio,
//           crossAxisSpacing: spacing),
//       header: ShortVideoSeriesWidget(
//         onTap: (name) {
//           controller.doSearch(name);
//         },
//       ),
//     );
//   }
// }
