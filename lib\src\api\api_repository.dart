import 'package:lib_base/config/app_config.dart';
import 'package:lib_base/config/env/env_config.dart';
import 'package:lib_base/config/net/base_response.dart';
import 'package:lib_base/model/http/base_page_list_response_model.dart';
import 'package:lib_base/model/http/get_sign_config_model.dart';
import 'package:lib_base/model/http/sys_config.dart';
import 'package:lib_base/model/http/ad_info.dart';
import 'package:lib_base/utils/business/app_util.dart';
import 'package:lib_base/model/user_info_model.dart';
import 'package:lib_base/utils/rsa_util.dart';
import 'package:yyb_flutter/model/http/area_school_model.dart';
import 'package:yyb_flutter/model/http/book_info_by_area_model.dart';
import 'package:lib_base/model/http/book_version_model.dart';
import 'package:yyb_flutter/model/http/chinese_model.dart';
import 'package:yyb_flutter/model/http/class_dynamic_response.dart';
import 'package:yyb_flutter/model/http/discover_data_model.dart';
import 'package:yyb_flutter/model/http/english_course_info.dart';
import 'package:yyb_flutter/model/http/english_recommend_activity.dart';
import 'package:yyb_flutter/model/http/example_get_example_response.dart';
import 'package:yyb_flutter/model/http/expand_video_item.dart';
import 'package:yyb_flutter/model/http/explain_phone_model.dart';
import 'package:yyb_flutter/model/http/glory_header_model.dart';
import 'package:yyb_flutter/model/http/head_info_model.dart';
import 'package:yyb_flutter/model/http/my_header_model.dart';
import 'package:yyb_flutter/model/http/news_user_receive_model.dart';
import 'package:yyb_flutter/model/http/order_campandju_model.dart';
import 'package:yyb_flutter/model/http/query_chinese_expand_model.dart';
import 'package:yyb_flutter/model/http/english_interest_info.dart';
import 'package:yyb_flutter/model/http/english_short_video_info.dart';
import 'package:lib_base/model/id_name_info.dart';
import 'package:yyb_flutter/model/http/read_model.dart';
import 'package:yyb_flutter/model/http/student_dynamic_model.dart';
import 'package:yyb_flutter/model/http/update_pwd_model.dart';
import 'package:yyb_flutter/model/http/video_model.dart';

import 'app_api.dart';

class ApiRepository {
  static Future<BaseResponse<SysConfig>> findByAppId() {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    return AppApi.instance().findByAppId(
        AppConfig.buildNumber.toString(),
        userInfoModel.obj?.memberCode ?? "",
        AppConfig.YYB_STUDENT_APPID.toString());
  }

  static Future<BaseResponse<List<AdInfo>>> listavailables(
      {required List<String> positionIds, required String userId}) {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    return AppApi.instance().listavailables(
        positionIds,
        userInfoModel.obj?.memberCode ?? "",
        AppConfig.buildNumber.toString(),
        userId);
  }

  // static Future<BaseResponse<List<AdvertiseInfo>>> queryadvertise({required String positionId,required String userId}){
  //   UserInfoModel userInfoModel=UserInfoModel.loadDefault();
  //   return AppApi.instance().queryadvertise(positionId,userInfoModel.obj?.memberCode??"",AppConfig.buildNumber.toString(),userId);
  // }

  static Future<BaseResponse<List<EnglishCourseInfo>>> queryEnglishCourse(
      {required String grade}) {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    String appId = AppConfig.YYB_STUDENT_APPID.toString();
    return AppApi.instance().queryEnglishCourse(
        grade,
        userInfoModel.obj?.memberCode ?? "",
        AppUtil.getAppMenuId("kecheng"),
        appId);
  }

  static Future<BaseResponse<List<EnglishInterestInfo>>>
      queryenglishinterestthree({
    required String grade,
  }) {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    String appId = AppConfig.YYB_STUDENT_APPID.toString();
    return AppApi.instance().queryenglishinterestthree(
        grade,
        userInfoModel.obj?.memberCode ?? "",
        AppUtil.getAppMenuId("english_xingqu"),
        appId,
        userInfoModel.obj?.uid ?? "",
        AppConfig.buildNumber.toString());
  }

  static Future<BaseResponse<EnglishShortVideoInfo>> pageappshortvideodata(
      {String? name, int qryType = 5, required int pageNo, int pageSize = 10}) {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    return AppApi.instance().pageappshortvideodata(
        qryType, userInfoModel.obj?.uid ?? "", pageNo, pageSize, name);
  }

  static Future<BaseResponse<EnglishRecommendActivity>> getRecommendActivity() {
    return AppApi.instance().getRecommendActivity();
  }

  static Future<BaseResponse<List<IdNameInfo>>> selectshortvideoallseries() {
    return AppApi.instance().selectshortvideoallseries();
  }

  static Future<BaseResponse<List<QueryChineseExpandModel>>> querychineseexpand(
      {required String grade,
      required String bookId,
      required String appMenu}) {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    String appId = AppConfig.YYB_STUDENT_APPID.toString();
    String userLevel = userInfoModel.obj?.memberCode ?? "";
    String versionNum = AppConfig.buildNumber.toString();
    return AppApi.instance().querychineseexpand(
        grade, userLevel, appMenu, appId, bookId, versionNum);
  }

  static Future<BaseResponse> getMyBestResult(
      {required String userId, required String activeId}) {
    return AppApi.instance().getMyBestResult(userId, activeId);
  }

  static Future<BaseResponse<List<AreaSchoolModel>>> queryAreaSchool(
      {String? areaNames}) {
    return AppApi.instance().queryAreaSchool(areaNames);
  }

  // @Query("subject") String? subject,
  // @Query("version") String? version,
  // @Query("grade") String? grade,
  // @Query("fascicule") String? fascicule
  static Future<BaseResponse<List<BookVersionModel>>> listbkversion(
      {String? subject, String? version, String? grade, String? fascicule}) {
    return AppApi.instance().listbkversion(subject, version, grade, fascicule);
  }

  // @Query("subject") String? subject,
  // @Query("areaCodes") String? areaCodes,
  // @Query("grade") String? grade,
  // @Query("fascicule") String? fascicule);
  static Future<BaseResponse<List<BookInfoByAreaModel>>>
      querybooknominatebyarea(
          {String? subject,
          String? areaCodes,
          String? grade,
          String? fascicule}) {
    return AppApi.instance()
        .querybooknominatebyarea(subject, areaCodes, grade, fascicule);
  }

  static Future<BaseResponse<ExplainPhoneModel>> explainPhone({
    required String type,
  }) {
    return AppApi.instance().explainPhone(type);
  }

  // @Field("puserId") String puserId,
  // @Field("value") int value,
  // @Field("content") String content,
  // @Field("filePath") String? filePath,
  // @Field("fileType") String? fileType,
  // @Field("userPhone") String? userPhone,
  // @Field("uinitId") String? uinitId,
  // @Field("unitName") String? unitName,
  // @Field("type") String? type,
  // @Field("moduleId") String? moduleId,
  // @Field("moduleName") String? moduleName,
  // @Field("remarks") String? remarks,
  static Future<BaseResponse> addadvice(
      {required int value,
      required String content,
      String? filePath,
      String? fileType,
      String? userPhone,
      String? unitId,
      String? unitName,
      String? moduleId,
      String? moduleName,
      String? type,
      String? subject,
      String? segment,
      String? version,
      String? grade,
      String? fascicule}) {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    return AppApi.instance().addadvice(
        userInfoModel.obj?.uid ?? "",
        value,
        content,
        filePath,
        fileType,
        userPhone,
        unitId,
        unitName,
        type,
        moduleId,
        moduleName,
        AppConfig.YYB_STUDENT_APPID,
        subject,
        segment,
        version,
        grade,
        fascicule);
  }

  static Future<BaseResponse> finduseradvices(
      {String? moduleId, int pageNo = 1, int pageSize = 10}) {
    UserInfoModel userInfoModel = UserInfoModel.loadDefault();
    return AppApi.instance().finduseradvices(
        userInfoModel.obj?.uid ?? "", moduleId, pageNo, pageSize);
  }

  static Future<BaseResponse<UpdatePwdModel>> changePass(
      {required String oldPwd, required String newPwd}) async {
    oldPwd =
        await RsaUtil.encodeStringWithPubKey(Env.envConfig.publicKey, oldPwd);
    newPwd =
        await RsaUtil.encodeStringWithPubKey(Env.envConfig.publicKey, newPwd);
    return AppApi.instance().changePass(oldPwd, newPwd, newPwd);
  }

  static Future<BaseResponse> verifyNewPhone(
      {required String phone, required String verifyCode}) async {
    return AppApi.instance().verifyNewPhone(phone, verifyCode);
  }

  static Future<BaseResponse> verifyOldPhone(
      {required String phone, required String verifyCode}) async {
    return AppApi.instance().verifyOldPhone(phone, verifyCode);
  }

  static Future<BaseResponse> close({required String password}) async {
    password =
        await RsaUtil.encodeStringWithPubKey(Env.envConfig.publicKey, password);
    return AppApi.instance().close(password);
  }

  static Future<BaseResponse<List<DiscoverDataModel>>> discovereddata(
      {required String userId, required String grade}) async {
    return AppApi.instance().discovereddata(userId, grade);
  }

  static Future<BaseResponse> checkuserblend(
      {required String userId, required String blendId}) async {
    return AppApi.instance().checkuserblend(userId, blendId);
  }

  static Future<BaseResponse<List<DisCoverItem>>> listcampdata(
      {required String grade}) async {
    return AppApi.instance().listcampdata(grade);
  }

  static Future<BaseResponse> applyjoinclazzstudentnumber(
      {required String studentName,
      required String clazzNo,
      String? studentNumber}) async {
    return AppApi.instance()
        .applyjoinclazzstudentnumber(studentName, clazzNo, studentNumber);
  }

  static Future<BaseResponse<List<MyHeaderModel>>> listmyhead(
      {required String? photo}) async {
    return AppApi.instance().listmyhead(photo);
  }

  static Future<BaseResponse<HeadInfoModel>> getheadinfo(
      {String? photo, required String level}) async {
    return AppApi.instance().getheadinfo(photo, level);
  }

  static Future<BaseResponse> usehead(
      {required String headId, String? glory}) async {
    String appId = AppConfig.YYB_STUDENT_APPID.toString();
    if (glory != "1") {
      glory = null;
    }
    return AppApi.instance().usehead(appId, headId, glory);
  }

  static Future<BaseResponse> redemptionhead(
      {required String headId, required String level}) async {
    String appId = AppConfig.YYB_STUDENT_APPID.toString();
    return AppApi.instance().redemptionhead(appId, headId, level);
  }

  static Future<BaseResponse<List<GloryHeaderModel>>> gloryList(
      {required String? photo}) async {
    return AppApi.instance().gloryList(photo);
  }

  static Future<BaseResponse<List<OrderCampandjuModel>>> listordercampandju(
      {required String userId}) async {
    return AppApi.instance().listordercampandju(userId);
  }

  static Future<BaseResponse> upclazzstudentnum(
      {required String userId,
      required String clazzId,
      required String isIdentity,
      int? stuNo}) async {
    return AppApi.instance()
        .upclazzstudentnum(userId, clazzId, isIdentity, stuNo);
  }

  static Future<BaseResponse<StudentDynamicModel>> getstudentdynamic(
      {required String userId,
      required String clazzId,
      required int pageNo,
      required int pageSize}) async {
    return AppApi.instance()
        .getstudentdynamic(userId, clazzId, pageNo, pageSize);
  }

  static Future<BaseResponse<ClassDynamicResponse>> getclazzstudentdynamic(
      {required String userId,
      required String clazzId,
      required int pageNo,
      required int pageSize}) async {
    return AppApi.instance()
        .getclazzstudentdynamic(userId, clazzId, pageNo, pageSize);
  }

  static Future<BaseResponse<List<ExampleGetExampleResponse>>>
      exampleGetexample(
          {required String province,
          required String city,
          required String address}) async {
    return AppApi.instance().exampleGetexample(province, city, address);
  }

  //  Future<BaseResponse<List<IdNameInfo>>> queryintgunitlist(
  //     @Query("bookId") String bookId,
  //   );
  static Future<BaseResponse<List<IdNameInfo>>> queryintgunitlist(
      {required String bookId}) async {
    return AppApi.instance().queryintgunitlist(bookId);
  }

  static Future<BaseResponse<BasePageListResponseModel<NewsUserReceiveModel>>>
      queryNewsUserReceive({
    required int pageNo,
    required int pageSize, // 显示数量(强烈建议20，已读和删除时只删除以20为显示数量条件的缓存)
  }) {
    return AppApi.instance().queryNewsUserReceive(pageNo, pageSize);
  }

  static Future<BaseResponse> deleteNewsUserReceive({
    required List<String> ids,
  }) {
    return AppApi.instance().deleteNewsUserReceive(ids, '');
  }

  static Future<BaseResponse> updateReadStatus({
    required List<String> ids,
  }) {
    return AppApi.instance().updateReadStatus(ids, '');
  }

  static Future<BaseResponse<ChineseModel>> onecmbookdata({
    required String id,
    required String userId,
  }) {
    return AppApi.instance().onecmbookdata(id, AppConfig.buildNumber, userId);
  }

  static Future<BaseResponse<List<ReadModel>>> listexrdresource({
    required String grade,
  }) {
    return AppApi.instance().listexrdresource(grade);
  }

  static Future<BaseResponse<VideoModel>> onelateststvideo() {
    return AppApi.instance().onelateststvideo();
  }

  static Future<BaseResponse> checkstudentgroupbuy({
    required String userId,
    required String memberId,
    required String areaCodes,
    required String enVersion,
  }) {
    return AppApi.instance().checkstudentgroupbuy(
      userId,
      memberId,
      areaCodes,
      enVersion,
    );
  }

  
  static Future<BaseResponse<GetSignConfigModel>> getsignconfig() {
    return AppApi.instance().getsignconfig();
  }


  
  /**
   * 获取用户收藏资源
   */
  static Future<BaseResponse<BasePageListResponseModel<ExpandVideoItem>>> expGetUserCollectResource({
    required String puserId,
    required int pageSize,
    required int pageIndex,
  }) {
    return AppApi.instance().expGetUserCollectResource(
      puserId,
      pageSize,
      pageIndex,
    );
  }
}
