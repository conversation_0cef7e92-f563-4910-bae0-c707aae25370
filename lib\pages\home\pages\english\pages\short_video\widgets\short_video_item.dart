// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:lib_base/config/route_name.dart';
// import 'package:lib_base/config/route_utils.dart';
// import 'package:lib_base/config/theme_config.dart';
// import 'package:lib_base/utils/business/app_util.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:lib_base/widgets/image/net_cache_image.dart';
// import 'package:yyb_flutter/common/route_define.dart';
// import 'package:yyb_flutter/src/generated/assets.dart';
// import 'package:yyb_flutter/model/http/english_short_video_info.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:yyb_flutter/pages/home/<USER>/english/pages/short_video/pages/short_video_play_page.dart';

// class ShortVideoItem extends StatelessWidget {
//   final ListObj obj;
//   final GestureTapCallback? onTap;
//   final double width;
//   const ShortVideoItem(
//       {super.key, required this.obj, this.onTap, required this.width});

//   @override
//   Widget build(BuildContext context) {
//     String image = obj.imgUrl ?? "";
//     String imageUrl = ImageUtil.getImageUrl(image);
//     var resourceList = obj.resourcesList ?? [];
//     String resourceName = "";
//     if (resourceList.isNotEmpty) {
//       resourceName = resourceList[0].name ?? "";
//     }
//     int likeNum = obj.likesNum?.toInt() ?? 0;
//     String likeNumStr = AppUtil.parseNum(likeNum, true);
//     return InkWell(
//       onTap: onTap,
//       child: Column(
//         children: [
//           // CachedNetworkImage(imageUrl: imageUrl),
//           BaseNetCacheImage(
//             imageUrl: imageUrl,
//             width: width,
//             fit: BoxFit.fitWidth,
//           ),
//           Padding(
//             padding: EdgeInsets.only(top: 6.0),
//             child: Text(
//               obj.name ?? "",
//               style: ThemeConfig.currentTheme.text14,
//             ),
//           ),
//           Expanded(
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Expanded(
//                     child: Text(resourceName,
//                         style: ThemeConfig.currentTheme.text13P1)),
//                 Row(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Padding(
//                       padding: EdgeInsets.only(right: 5.r),
//                       child: Image.asset(
//                         Assets.imagesShortLikeIcon,
//                         width: 15.r,
//                       ),
//                     ),
//                     Text(likeNumStr, style: ThemeConfig.currentTheme.text13P1),
//                   ],
//                 )
//               ],
//             ),
//           )
//         ],
//       ),
//     );
//   }
// }
