// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/config/app_config.dart';
// import 'package:lib_base/log/log.dart';
// import 'package:lib_base/model/http/ad_info.dart';
// import 'package:lib_base/model/http/book_study_record_vo.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:lib_base/widgets/draggable_widget/drag_move_box.dart';
// import 'package:lib_base/widgets/image/net_cache_image.dart';
// import 'package:lib_base/widgets/refresh/refresh_view.dart';
// import 'package:lib_base/model/http/book_info_detail.dart';
// import 'package:yyb_flutter/pages/home/<USER>/widget/home_ad_banner.dart';
// import 'package:lib_base/widgets/business/title_row/has_more_title_row.dart';
// import 'package:yyb_flutter/pages/home/<USER>/math/pages/math/widgets/module_item.dart';
// import 'package:yyb_flutter/providers/chinese_math/math/math_page_provider.dart';

// // MathFragment
// class MathPage extends ConsumerWidget {
//   const MathPage({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     MathPageNotifier controller = ref.watch(mathPageNotifierProvider.notifier);
//     return Stack(
//       children: [
//         _mainScreenWidget(context, ref, controller),
//         DragMoveBox(
//           child: ValueListenableBuilder(
//             builder: (_, value, child) {
//               Logger.info("=======  build math ui :  value:$value");
//               if (value == null) {
//                 return SizedBox.shrink();
//               } else {
//                 String image = value.stuIcon ?? "";
//                 String imageUrl = ImageUtil.getImageUrl(image);
//                 return BaseNetCacheImage(
//                   imageUrl: imageUrl,
//                   width: 80.r,
//                   height: 80.r,
//                 );
//               }
//             },
//             valueListenable: controller.mathAiNotifier,
//           ), //需要拖动的控件
//           offset: Offset(220.w, 250.h),
//         )
//       ],
//     );
//   }

//   /**
//    * 检测是否有mathai
//    */
//   void _checkMathAi(List<BookColumnList> columns, MathPageNotifier controller) {
//     var module95;
//     columns.forEach((element) {
//       (element.bookModuleInfoList ?? []).forEach((m) {
//         if (m.moduleId == "95") {
//           module95 = m;
//         }
//       });
//     });
//     //数学ai， 不展示在列表， 而是作为可拖拽的组件展示
//     Future.delayed(Duration(milliseconds: 500), () {
//       controller.mathAiNotifier.value = module95;
//     });
//   }

//   Widget _mainScreenWidget(
//       BuildContext context, WidgetRef ref, MathPageNotifier controller) {
//     return RefreshView(
//         sliversBuilder: (_) {
//           List<AdInfo> courseBannerAds = controller.getPageBannerAds();
//           BookInfoDetail? bookInfoDetail = controller.bookInfoDetail;
//           List<BookColumnList> columns = bookInfoDetail?.bookColumnList ?? [];
//           _checkMathAi(columns, controller);
//           return [
//             SliverToBoxAdapter(
//               child: HomeAdBanner(
//                 ads: courseBannerAds,
//               ),
//             ),
//             ..._views(columns),
//           ];
//         },
//         controller: controller);
//   }

//   List<Widget> _views(List<BookColumnList> columns) {
//     List<Widget> children = [];
//     if (columns.isNotEmpty) {
//       for (int i = 0; i < columns.length; i++) {
//         BookColumnList c = columns[i];
//         String name = c.name ?? "";
//         List<BookModuleInfoList> modules = c.bookModuleInfoList ?? [];

     

//         if (modules.isNotEmpty) {
//           //标题
//           children.add(SliverToBoxAdapter(
//             child: Padding(
//               padding: EdgeInsets.only(top: 15.h, bottom: 15.h),
//               child: HasMoreTitleRow(
//                 name: name,
//                 isMore: false,
//               ),
//             ),
//           ));
//           //内容
//           // String type=c.type??"";
//           // if(type=="1"){//同步学习
//           //
//           // }else if(type=="2"){//趣味数学
//           //
//           // }
//           children.add(SliverToBoxAdapter(
//             child: ModuleItemWrap(modules: modules),
//           ));
//         }
//       }
//     }
//     return children;
//   }
// }
