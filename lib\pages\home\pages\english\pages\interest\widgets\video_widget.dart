// import 'package:flutter/cupertino.dart';
// import 'package:lib_base/config/theme_config.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:lib_base/widgets/image/net_cache_image.dart';
// import 'package:yyb_flutter/src/extention/build_context_ext.dart';
// import 'package:yyb_flutter/model/http/english_interest_info.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';

// class VideoWidget extends StatelessWidget {
//   final Obj obj;
//   const VideoWidget({super.key, required this.obj});

//   @override
//   Widget build(BuildContext context) {
//     String image = obj.image ?? "";
//     String imageUrl = ImageUtil.getImageUrl(image);
//     double width = 150.r;
//     String title = obj.name ?? "";
//     String entryNumStr = obj.entryNum?.toString() ?? "0";
//     return Container(
//       margin: EdgeInsets.only(right: 20.r),
//       width: width,
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           Stack(
//             children: [
//               BaseNetCacheImage(
//                 imageUrl: imageUrl,
//                 width: width,
//               ),
//               Positioned(
//                   bottom: 0,
//                   right: 10,
//                   child: Text(
//                     "$entryNumStr${context.string.textOfVideoAll}",
//                     style: ThemeConfig.currentTheme.text14P2,
//                   )),
//             ],
//           ),
//           Text(
//             title,
//             style: ThemeConfig.currentTheme.text14,
//             maxLines: 1,
//           )
//         ],
//       ),
//     );
//   }
// }
