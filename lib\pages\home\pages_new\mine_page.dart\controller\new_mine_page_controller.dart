import 'package:flutter/material.dart';
import 'package:lib_base/config/app_config.dart';
import 'package:lib_base/config/route_name.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/model/http/ad_info.dart';
import 'package:lib_base/model/http/sys_config.dart';
import 'package:lib_base/providers/sysconfig_provider.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';
import 'package:lib_base/resource/shared_preferences_keys.dart';
import 'package:lib_base/utils/business/ad_util.dart';
import 'package:lib_base/utils/business/app_util.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:lib_base/utils/wx_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:yyb_flutter/common/route_define.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:yyb_flutter/src/api/api_repository.dart';
import 'package:yyb_flutter/src/generated/assets.gen.dart';

part 'new_mine_page_controller.g.dart';

@riverpod
class NewMinePageController extends _$NewMinePageController {
  ScrollController scrollController = ScrollController();
  ValueNotifier<bool> showNavColor = ValueNotifier(false);
  ValueNotifier<List<AdInfo>> ads = ValueNotifier([]);

  List commonList = [
    {
      'title': '错题本',
      'img': AppAssets.images.mineCommonIconWrongQuestion.keyName,
      'type': 'wrongQuestion'
    },
    {
      'title': '学生资料',
      'img': AppAssets.images.mineCommonIconStudentData.keyName,
      'type': 'studentData'
    },
    {
      'title': '团购',
      'img': AppAssets.images.mineCommonIconGroupBuying.keyName,
      'type': 'groupBuying'
    },
    {
      'title': '券福利社',
      'img': AppAssets.images.mineCommonIconWelfareSociety.keyName,
      'type': 'welfareSociety'
    },
    {
      'title': '我的订单',
      'img': AppAssets.images.mineCommonIconOrder.keyName,
      'type': 'order'
    },
    {
      'title': '我的地址',
      'img': AppAssets.images.mineCommonIconAddress.keyName,
      'type': 'address'
    },
  ];
  List moreList = [
    {
      'title': '帮助与反馈',
      'img': AppAssets.images.mineMoreIconHelp.keyName,
      'type': 'help'
    },
    {
      'title': '我的收藏',
      'img': AppAssets.images.mineMoreIconCollect.keyName,
      'type': 'collect'
    },
    {
      'title': '家长必看',
      'img': AppAssets.images.mineMoreIconParentLook.keyName,
      'type': 'parentLook'
    },
    {
      'title': '监护密码',
      'img': AppAssets.images.mineMoreIconGuardianshipPassword.keyName,
      'type': 'guardianshipPassword'
    },
    {
      'title': '分享有礼',
      'img': AppAssets.images.mineMoreIconShare.keyName,
      'type': 'share'
    },
    {
      'title': '推广大使',
      'img': AppAssets.images.mineMoreIconPromotion.keyName,
      'type': 'promotion'
    },
    {
      'title': '设置',
      'img': AppAssets.images.mineMoreIconSetting.keyName,
      'type': 'setting'
    },
  ];

  late var _userInfo;
  int build() {
    return 0;
  }

  initController() {
    _userInfo = ref.watch(userInfoNotifierProvider);
    scrollController.addListener(() {
      showNavColor.value = scrollController.offset > 0;
    });
    loadData();
    SysConfig sysConfig = ref.read(sysConfigProvider);
    bool showGroupBuy = sysConfig.groupBuyStatus == '1';
    var userInfo = ref.watch(userInfoNotifierProvider);
    String enVersion = userInfo.getEnVersion();
    Logger.info("=========enVersion:$enVersion");
    bool showRj = "rjxqd" == enVersion || enVersion.startsWith("pep");
    if (showRj) {
      commonList.add(
        {
          'title': '人教点读券',
          'img': AppAssets.images.mineCommonIconPointReading.keyName,
          'type': 'pointReading'
        },
      );
    }
    if (showGroupBuy) {
      commonList.insert(
        0,
        {
          'title': '优惠券',
          'img': AppAssets.images.mineCommonIconCoupon.keyName,
          'type': 'coupon'
        },
      );
    }
  }

  loadData() {
    ApiRepository.listavailables(
      positionIds: [AdUtil.getMineBannerAd()],
      userId: _userInfo.userId,
    ).then((response) {
      if (response.isSuccess && response.isDataNotNull) {
        ads.value = response.dataNotNull;
      }
    });
  }

  //顶部按钮点击
  topBtnClick(String type) {
    if (type == 'translate') {
      //翻译
      showToast('敬请期待');
    } else if (type == 'sign') {
      if (AppConfig.isNeedHideForUnfinished) {
        showToast('敬请期待');
        return;
      }
      toPage(RouteName.signStudentPage);
    } else if (type == 'message') {
      toPage(ERouteName.myMessage);
    } else if (type == 'setting') {
      toPage(ERouteName.setting);
    }
  }

  //会员点击
  vipClick(SysConfig sysConfig) {
    String pageName = StorageManager.sharedPreferences
                .getString(SharedPreferencesKeys.newVipStatus) ==
            "1"
        ? "indexNew.html"
        : "";
    toInteractWebviewModulePage(
        moduleId: "20",
        showBack: true,
        pageName: pageName,
        othermap: {
          "type": AppUtil.getRecommMemberType(_userInfo, 'english', sysConfig),
          "channel": "1",
          "promote_group": "993",
        });
  }

  //资产-积分和钱包
  assetsItemClick(int type) {
    if (type == 0) {
      toPage(RouteName.myPoint);
    } else {
      toPage(ERouteName.myWallet);
    }
  }

  //功能区点击
  functionalAreaClick(String type) {
    Logger.info("======= type:$type");
    if (type == 'bookCity') {
      //e学书城
      toInteractWebviewModulePage(moduleId: "33", showBack: true, othermap: {
        "userId": _userInfo.userId,
      });
    } else if (type == 'shoppingMall') {
      //积分商城
      toInteractWebviewModulePage(moduleId: "12", showBack: true);
    } else if (type == 'eBao') {
      //我的e宝
      toInteractWebviewModulePage(
          moduleId: "81",
          showBack: true,
          pageName: "index.html",
          othermap: {"userId": _userInfo.userId});
    } else if (type == 'parent') {
      //绑定家长端-查看学情
      WxUtil.parentSeeStydyInfo();
    } else if (type == 'service') {
      //加入服务群
      WxUtil.parentJoinServiceGroup(_userInfo.userId);
    } else if (type == 'wrongQuestion') {
      //错题本
      toInteractWebviewModulePage(
          moduleId: "44",
          showBack: true,
          pageName: "index.html",
          othermap: {"userId": _userInfo.userId, "subject": "english"});
    } else if (type == 'studentData') {
      //学生资料
      toPage(RouteName.userInfo);
    } else if (type == 'groupBuying') {
      //团购
      toInteractWebviewModulePage(
          moduleId: "53",
          showBack: true,
          pageName: "index.html",
          othermap: {"userId": _userInfo.userId});
    } else if (type == 'welfareSociety') {
      //券福利社
      toInteractWebviewModulePage(
          moduleId: "70",
          showBack: true,
          pageName: "index.html",
          othermap: {"userId": _userInfo.userId});
    } else if (type == 'order') {
      //我的订单
      toPage(ERouteName.myOrder);
    } else if (type == 'address') {
      //我的地址
      toInteractWebviewUrlPage(
          url: "${AppConfig.PROFILE_MY_ADDRESS}${_userInfo.obj.uid ?? ""}");
    } else if (type == 'coupon') {
      //优惠券
      toInteractWebviewModulePage(moduleId: "43", showBack: true);
    } else if (type == 'pointReading') {
      //人教点读券
      toInteractWebviewModulePage(
          moduleId: "20",
          showBack: true,
          pageName: "myCoupon.html",
          othermap: {"userId": _userInfo.userId});
    } else if (type == 'help') {
      //帮助与反馈
      toPage(RouteName.helpAndSupport);
    } else if (type == 'collect') {
      //我的收藏
      if (AppConfig.isNeedHideForUnfinished) {
        showToast("敬请期待");
        return;
      }
      toPage(RouteName.myFavorite);
    } else if (type == 'parentLook') {
      //家长必看
      toInteractWebviewModulePage(
          moduleId: "71",
          showBack: true,
          pageName: "index.html",
          othermap: {"userId": _userInfo.userId});
    } else if (type == 'guardianshipPassword') {
      //监护密码
      toPage(RouteName.managerPassword);
    } else if (type == 'share') {
      //分享有礼
      if (AppConfig.isNeedHideForUnfinished) {
        showToast("敬请期待");
        return;
      }
      toPage(ERouteName.shareWithGift);
    } else if (type == 'promotion') {
      //推广大使
      toInteractWebviewModulePage(
          moduleId: "77",
          showBack: true,
          pageName: "index.html",
          othermap: {"userId": _userInfo.userId});
    } else if (type == 'setting') {
      //设置
      toPage(ERouteName.setting);
    } else {
      showToast('敬请期待');
    }
  }
}
