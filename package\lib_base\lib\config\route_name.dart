import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/pages/camera_scanner/camera_scanner_page.dart';
import 'package:lib_base/pages/exam/page/exercise/page/exercise_exam/exercise_exam_page.dart';
import 'package:lib_base/pages/exam/page/exercise/page/report/exercise_report_page.dart';
import 'package:lib_base/pages/exam/page/exercise/page/review_question/review_exercise_question_page.dart';
import 'package:lib_base/pages/exam/page/exercise/select_exercise_page.dart';
import 'package:lib_base/pages/exam/page/not_volume_structure/not_volume_structure_page.dart';
import 'package:lib_base/pages/exam/page/rjdh/page/rjdh_pronounced_correct/page/rjdh_pronounced_report_page.dart';
import 'package:lib_base/pages/exam/page/rjdh/page/rjdh_pronounced_correct/rjdh_pronounced_correct_page.dart';
import 'package:lib_base/pages/exam/page/rjdh/page/rjdh_report_page/rjdh_report_page.dart';
import 'package:lib_base/pages/exam/page/rjdh/rjdh_page.dart';
import 'package:lib_base/pages/exam/page/statistics_test/statistics_test_page.dart';
import 'package:lib_base/pages/exam/page/volume_structure/page/report/page/review_question/review_question_page.dart';
import 'package:lib_base/pages/exam/page/volume_structure/page/report/unit_evaluation_report/unit_evaluation_report_page.dart';
import 'package:lib_base/pages/exam/page/volume_structure/volume_structure_page.dart';
import 'package:lib_base/pages/exam/page/word_match/page/match_result/match_result_page.dart';
import 'package:lib_base/pages/exam/page/word_match/word_match_page.dart';
import 'package:lib_base/pages/exam/page/wrong_exercise/page/review_wrong_question/review_wrong_question_page.dart';
import 'package:lib_base/pages/exam/page/wrong_exercise/page/wrong_exercise_report/wrong_exercise_report_page.dart';
import 'package:lib_base/pages/exam/page/wrong_exercise/wrong_exercise_page.dart';
import 'package:lib_base/pages/exam/widget/word_test/word_test_page.dart';
import 'package:lib_base/pages/network_bad_guid/network_bad_guid_page.dart';
import 'package:lib_base/pages/speech_low_scores_guid/speech_low_scores_guid_page.dart';
import 'package:lib_base/pages/speech_record_issue_guid/speech_record_issue_guid_page.dart';
import 'package:lib_base/pages/video_page/english_video_play/english_video_play_page.dart';
import 'package:lib_base/pages/video_page/full_screen_video/full_screen_video_page.dart';
import 'package:lib_base/pages/video_page/learn_course_video_play/learn_course_video_play_page.dart';
import 'package:lib_base/pages/video_page/math_video_play/math_video_play_page.dart';
import 'package:lib_base/pages/webview/interact_webview_download_zip_page.dart';
import 'package:lib_base/pages/webview/interact_webview_module_page.dart';
import 'package:lib_base/pages/webview/interact_webview_url_page.dart';
import 'package:lib_base/pages/webview/normal_webview_page.dart';
import 'package:lib_base/pages/webview/webview_mxin/asset_webview_mixin.dart';
import 'package:lib_base/pages/webview/webview_mxin/url_webview_mixin.dart';
import 'package:lib_base/widgets/video/full_screen_page.dart';

class RouteName {
  static const String splash = '/';
  static const String home = '/home';
  static const String webView = '/webView';
  static const String guid = '/guid';
  static const String login = '/login';
  static const String regGradle = "/regGradle";
  static const String bindPhone = "/bindPhone";
  static const String fullScreenPage = "/fullScreen";
  static const String myVip = "/myVip";
  static const String userInfo = "userInfo";
  static const String interactWebviewModulePage = "/interactWebviewModulePage";
  static const String interactWebViewDownloadZipPage = "/interactWebViewDownloadZipPage";
  static const String interactWebviewUrlPage = "/interactWebviewUrlPage";
  static const String joinClass = "joinClass";
  static const String myClass = "myClass";
  static const String cameraScanner = "/cameraScanner";
  static const String helpAndSupport = "helpAndSupport";
  static const String feedback = "feedback";
  static const String speechLowScoreGuid = "/speechLowScoreGuid";
  static const String speechRecordIssueGuid = "/speechRecordIssueGuid";
  static const String networkBadGuid = "/networkBadGuid";
  static const String speechEvaluating = "/speechEvaluating";
  static const String fullScreenVideo = "/fullScreenVideo";
  static const String videoPlayPage = "/videoPlayPage";
  static const String courseAnimation = "/courseAnimation";
  static const String listenCourse = "/listenCourse";
  static const String happyPlayer = "/happyPlayer";
  static const String interactiveReading = "/interactiveReading";
  static const String volumeStructureExam = "/volumeStructureExam";
  static const String notVolumeStructureExam = "/notVolumeStructureExam";
  static const String unitEvaluationReport = "/unitEvaluationReport";
  static const String reviewQuestion = "reviewQuestion";
  static const String wrongExercisePage = "/wrongExercisePage";
  static const String wrongExerciseReportPage = "wrongExerciseReportPage";
  static const String wordDictation = "/word_dictation";
  static const String pictureBook = "/picture_book";
  static const String reviewWrongQuestion = "wrongQuestionReviewInfo";
  static const String exerciseReport = "/exerciseReport";
  static const String exerciseExam = "/exerciseExam";
  static const String reviewExerciseQuestion = "reviewExerciseQuestion";
  static const String selectExercise = "/selectExercise";
  static const String englishVideoPlayPage = "/englishVideoPlay";
  static const String myPracticeWork = "/myPracticeWork";
  static const String resetManagerPwd = "resetManagerPwd";
  static const String unitSyncExerciseMain = '/unitSyncExerciseMain';
  static const String unitSyncExerciseReceive = '/unitSyncExerciseReceive';
  static const String coursePlayer = "coursePlayer";
  static const String wordRemember = "/word_remember";
  static const String stuHomeworkSpeech = '/stuHomeworkSpeech';
  static const String newStuHomeworkSpeech = '/newStuHomeworkSpeech';
  static const String specialSpeech = "specialSpeech";
  static const String wordDetailPreview = '/wordDetailPreview';
  static const String readBookDetail = '/readBookDetail';
  static const String wordDictationTest = "word_dictation_test";
  static const String wordDictationTestReview = "word_dictation_test_review";
  static const String WordTestPage = "/WordTestPage";
  static const String reciteTheTextPage = '/reciteTheTextPage';
  static const String knowledgeClearanceHome = '/knowledgeClearanceHome';
  static const String bilingualReading = '/bilingualReadingPage';
  //统测活动
  static const String statisticsTestPage = "/StatisticsTestPage";
  //趣味练习
  static const String interestExercisesPage = '/interestExercisesPage';
  static const String chineseSwitchBook = "chineseSwitchBook";

  //巧记单词考试活动
  static const String wordMatch = "/wordMatch";

  static const String matchResult = "matchResult";
  static const String myPoint = "myPoint";
  static const String audioPlayPage = 'audioPlayPage'; //音频播放页
  static const String grammarExplanation = '/grammarExplanation'; // 语法讲解
  static const String basicTraining = '/basicTraining'; //  基础训练

  static const String homeWorkSpeechPage = '/homeWorkSpeech';
  static const String homeworkSpeechRecitePage = 'homeworkSpeechRecite';
  static const String dailySentence = "/dailySentence";

  //精听课文
  static const String intensiveListening = "/intensiveListening";

  //美文阅读 - 列表
  static const String readingBeautifulProse = "/readingBeautifulProse";

  //美文阅读 - 详情
  static const String readingBeautifulProseDetail = 'readingBeautifulProseDetail';

  //阶梯阅读 - 首页
  static const String stepRead = "/stepRead";

  static const String listenArticleDetail = "/listen_article_detail";

  static const String unitSynchronisedLearning = "/unitSynchronisedLearning";

  static const String englishLearning = "englishLearning";

  static const String managerPassword = "/managerPassword";

  // static String accountSecurity = "accountSecurity";
  // static String brainWordMemory = "/brainWordMemory"; // 全脑记词

  static const String accountSecurity = "accountSecurity";
  static const String brainWordMemory = "/brainWordMemory"; // 全脑记词
  static const String happlyListeningTime = "/happlyListeningTime"; // 乐听一刻
  //人机对话
  static const String rjdhExercisePage = "/rjdhExercisePage";
  static const String rjdhExerciseReportPage = "rjdhExerciseReportPage";
  static const String grammerOnePointPass = "/grammerOnePointPass";
  static const String rjdhPronouncedCorrectPage = "rjdhPronouncedCorrectPage";
  static const String rjdhPronouncedReportPage = "rjdhPronouncedReportPage";

  //语文-课文点读
  static const String textReadingPoints = "/textReadingPoints";
  //语文-课文点读详情
  static const String readingPointsDetailsPage = 'readingPointsDetailsPage';

  static const String learnCourseVideoPlay = "/learnCourseVideoPlay"; //训练营视频播放
  static const String unitIintegratedLearning = "/unitIintegratedLearning"; // 单元整体学
  static const String signStudentPage = "/signStudentPage"; // 学生签到
  // 拼音街
  static const String pinyinStreet = "/pinyinStreet"; // 拼音街首页

  static const String wordRememberReview = "word_remember_review";

  // 数学_知识动画
  static const String knowledgeAnimationPage = '/knowledgeAnimationPage';

  // 数学_h5 视频页
  static const String mathVideoPlayPage = '/mathVideoPlayPage';
  
  // 我的知识薄弱点
  static const String knowledgeWeaknessPage = '/knowledgeWeaknessPage';

    static String myFavorite = "myFavorite"; // 我的收藏
}

final Map<String, GoRouterWidgetBuilder> _baseRouteBuilderMap = {
  RouteName.volumeStructureExam: (_, state) {
    VolumeStructurePageParam param = state.extra as VolumeStructurePageParam;
    return VolumeStructurePage(
      param: param,
    );
  },
  RouteName.notVolumeStructureExam: (_, state) {
    NotVolumeStructurePageParam param = state.extra as NotVolumeStructurePageParam;
    return NotVolumeStructurePage(
      param: param,
    );
  },
  RouteName.unitEvaluationReport: (_, state) {
    UnitEvaluationReportPageParam param = state.extra as UnitEvaluationReportPageParam;
    return UnitEvaluationReportPage(
      param: param,
    );
  },
  RouteName.reviewQuestion: (_, state) {
    ReviewQuestionPageParam param = state.extra as ReviewQuestionPageParam;
    return ReviewQuestionPage(
      param: param,
    );
  },
  RouteName.wrongExercisePage: (_, state) {
    WrongExercisePageParam param = state.extra as WrongExercisePageParam;
    return WrongExercisePage(
      param: param,
    );
  },
  RouteName.wrongExerciseReportPage: (_, state) {
    WrongExerciseReportPageParam param = state.extra as WrongExerciseReportPageParam;
    return WrongExerciseReportPage(
      param: param,
    );
  },
  RouteName.reviewWrongQuestion: (_, state) {
    ReviewWrongQuestionPageParam param = state.extra as ReviewWrongQuestionPageParam;
    return ReviewWrongQuestionPage(
      param: param,
    );
  },
  RouteName.exerciseReport: (_, state) {
    ExerciseReportPageParam param = state.extra as ExerciseReportPageParam;
    return ExerciseReportPage(
      param: param,
    );
  },
  RouteName.exerciseExam: (_, state) {
    ExerciseExamPageParam param = state.extra as ExerciseExamPageParam;
    return ExerciseExamPage(
      param: param,
    );
  },
  RouteName.reviewExerciseQuestion: (_, state) {
    ReviewExerciseQuestionPageParam param = state.extra as ReviewExerciseQuestionPageParam;
    return ReviewExerciseQuestionPage(
      param: param,
    );
  },
  RouteName.selectExercise: (_, state) {
    SelectExercisePageParam param = state.extra as SelectExercisePageParam;
    return SelectExercisePage(param: param);
  },
  RouteName.englishVideoPlayPage: (_, state) {
    EnglishVideoPlayPageParam param = state.extra as EnglishVideoPlayPageParam;
    return EnglishVideoPlayPage(param: param);
  },
  RouteName.webView: (_, state) {
    String url = state.uri.queryParameters['url']!;
    String title = state.uri.queryParameters['title']!;
    String showAppBar = state.uri.queryParameters['showAppBar']!;
    String hasStatus = state.uri.queryParameters['hasStatus']!;
    return NormalWebViewPage(
      url: url,
      title: title,
      showAppBar: showAppBar == 'true',
      hasStatus: hasStatus == 'true',
    );
  },
  RouteName.fullScreenVideo: (_, state) {
    String videoUrl = state.uri.queryParameters['videoUrl']!;
    String? videoImageUrl = state.uri.queryParameters['videoImageUrl'];
    bool autoPlay = state.uri.queryParameters['autoPlay'] == 'true';
    bool isFile = state.uri.queryParameters['isFile'] == 'true';
    return FullScreenVideoPage(
      videoUrl: videoUrl,
      videoImageUrl: videoImageUrl,
      autoPlay: autoPlay,
      isFile: isFile,
    );
  },
  RouteName.fullScreenPage: (_, state) {
    Widget extra = state.extra as Widget;
    String? isPortrait = state.uri.queryParameters['isPortrait'];
    return FullScreenPage(child: extra, isPortrait: isPortrait == "true");
  },
  RouteName.networkBadGuid: (_, state) => NetworkBadGuidPage(),
  RouteName.interactWebviewModulePage: (_, state) {
    Map<String, String> params = {...state.uri.queryParameters};
    String moduleId = params['moduleId']!;
    params.remove("moduleId");
    String? pageName = params['pageName'];
    params.remove("pageName");
    String? id = params['id'];
    params.remove("id");
    String? showBack = params['showBack'];
    params.remove("showBack");
    String? hasStatus = params['hasStatus'];
    params.remove("hasStatus");
    String? isLandscape = params['isLandscape'];
    params.remove("isLandscape");
    String? webLoading = params['webLoading'];
    params.remove("webLoading");
    String? hasVideo = params['hasVideo'];
    params.remove("hasVideo");
    return InteractWebViewModulePage(
      pageParam: ModulePageParam(
        moduleId: moduleId,
        pageName: pageName,
        id: id,
        showBack: showBack == 'true',
        hasStatus: hasStatus == 'true',
        othermap: params,
        isLandscape: isLandscape == "true",
        webLoading: (webLoading?.isEmpty ?? true) || webLoading == "true",
        hasVideo: hasVideo == "true",
      ),
    );
  },
  RouteName.interactWebViewDownloadZipPage: (_, state) {
    Map<String, String> params = {...state.uri.queryParameters};
    String downloadUrl = params['downloadUrl']!;
    params.remove("downloadUrl");
    String? indexName = params['indexName'];
    params.remove("indexName");
    String resourceId = params['resourceId']!;
    params.remove("resourceId");
    String? showBack = params['showBack'];
    params.remove("showBack");
    String? isLandscape = params['isLandscape'];
    params.remove("isLandscape");
    return InteractWebViewDownloadZipPage(
      pageParam: DownloadZipPageParam(
          downloadUrl: downloadUrl,
          indexName: indexName,
          resourceId: resourceId,
          showBack: showBack == 'true',
          othermap: params,
          isLandscape: isLandscape == "true"),
    );
  },
  RouteName.interactWebviewUrlPage: (_, state) {
    Map<String, String> params = {...state.uri.queryParameters};

    String url = params['url']!;
    params.remove("url");
    String? pageName = params['pageName'];
    params.remove("pageName");
    String? id = params['id'];
    params.remove("id");
    String? hasStatus = params['hasStatus'];
    params.remove("hasStatus");
    String? showBack = params['showBack'];
    params.remove("showBack");
    String? isLandscape = params['isLandscape'];
    params.remove("isLandscape");
    return InteractWebViewUrlPage(
      pageParam: UrlPageParam(
        pageName: pageName,
        id: id,
        hasStatus: hasStatus == 'true',
        showBack: showBack == 'true',
        isLandscape: isLandscape == "true",
        othermap: params,
        url: url,
      ),
    );
  },
  RouteName.cameraScanner: (_, state) {
    return CameraScannerPage();
  },
  RouteName.speechRecordIssueGuid: (_, state) {
    return SpeechRecordIssueGuidPage();
  },
  RouteName.speechLowScoreGuid: (_, state) {
    return SpeechLowScoreGuidPage();
  },
  RouteName.WordTestPage: (_, state) {
    WordTestPageParam param = state.extra as WordTestPageParam;
    return WordTestPage(
      param: param,
    );
  },
  RouteName.statisticsTestPage: (_, state) {
    StatisticsTestPageParam param = state.extra as StatisticsTestPageParam;
    return StatisticsTestPage(
      param: param,
    );
  },
  RouteName.wordMatch: (_, state) {
    WordMatchPageParam param = state.extra as WordMatchPageParam;
    return WordMatchPage(
      param: param,
    );
  },
  RouteName.matchResult: (_, state) {
    MatchResultPageParam param = state.extra as MatchResultPageParam;
    return MatchResultPage(
      param: param,
    );
  },
  RouteName.rjdhExercisePage: (_, state) {
    RjdhPageParam param = state.extra as RjdhPageParam;
    return RjdhPage(
      param: param,
    );
  },
  RouteName.rjdhExerciseReportPage: (_, state) {
    RjdhReportPageParam param = state.extra as RjdhReportPageParam;
    return RjdhReportPage(
      param: param,
    );
  },
  RouteName.rjdhPronouncedCorrectPage: (_, state) {
    RjdhPronouncedCorrectPageParam param = state.extra as RjdhPronouncedCorrectPageParam;
    return RjdhPronouncedCorrectPage(
      param: param,
    );
  },
  RouteName.rjdhPronouncedReportPage: (_, state) {
    RjdhPronouncedReportPageParam param = state.extra as RjdhPronouncedReportPageParam;
    return RjdhPronouncedReportPage(
      param: param,
    );
  },
  RouteName.learnCourseVideoPlay: (_, state) {
    LearnCourseVideoPlayPageParam param = state.extra as LearnCourseVideoPlayPageParam;
    return LearnCourseVideoPlayPage(
      param: param,
    );
  },
  RouteName.mathVideoPlayPage: (_, state) {
    MathVideoPlayPageParam param = state.extra as MathVideoPlayPageParam;
    return MathVideoPlayPage(
      param: param,
    );
  },
};

final List<RouteBase> baseRoutes = [
  _goRoute(RouteName.volumeStructureExam),
  _goRoute(RouteName.notVolumeStructureExam),
  _goRoute(RouteName.unitEvaluationReport, routes: [_goRoute(RouteName.reviewQuestion)]),
  _goRoute(RouteName.wrongExercisePage, routes: [
    _goRoute(RouteName.wrongExerciseReportPage, routes: [_goRoute(RouteName.reviewWrongQuestion)])
  ]),
  _goRoute(RouteName.exerciseReport, routes: [_goRoute(RouteName.reviewExerciseQuestion)]),
  _goRoute(RouteName.exerciseExam),
  _goRoute(RouteName.selectExercise),
  _goRoute(RouteName.englishVideoPlayPage),
  _goRoute(RouteName.fullScreenPage),
  _goRoute(RouteName.interactWebviewModulePage),
  _goRoute(RouteName.interactWebViewDownloadZipPage),
  _goRoute(RouteName.interactWebviewUrlPage),
  _goRoute(RouteName.cameraScanner),
  _goRoute(RouteName.speechLowScoreGuid),
  _goRoute(RouteName.speechRecordIssueGuid),
  _goRoute(RouteName.networkBadGuid),
  _goRoute(RouteName.fullScreenVideo, isLandscape: true),
  _goRoute(RouteName.webView),
  _goRoute(RouteName.WordTestPage),
  _goRoute(RouteName.statisticsTestPage),
  _goRoute(RouteName.wordMatch, routes: [_goRoute(RouteName.matchResult)]),
  _goRoute(RouteName.rjdhExercisePage, routes: [
    _goRoute(RouteName.rjdhExerciseReportPage, routes: [
      _goRoute(RouteName.rjdhPronouncedCorrectPage, routes: [_goRoute(RouteName.rjdhPronouncedReportPage)])
    ])
  ]),
  _goRoute(RouteName.learnCourseVideoPlay, isLandscape: true),
  _goRoute(
    RouteName.mathVideoPlayPage,
  ),
];

GoRoute _goRoute(String path, {List<GoRoute>? routes, bool? isLandscape}) {
  // Logger.info("============= path:$path");
  return goRoute(path, _baseRouteBuilderMap, routes: routes, isLandscape: isLandscape);
}

GoRoute goRoute(String path, Map<String, GoRouterWidgetBuilder> routeMap, {List<GoRoute>? routes, bool? isLandscape}) {
  if (routeMap[path] == null) {
    Logger.error("未注册路由和页面的映射关系：$path");
  }
  return GoRoute(
      path: path,
      name: path,
      routes: routes ?? [],
      pageBuilder: (_, state) {
        bool? needLandscape = isLandscape;
        if (needLandscape == null) {
          if (state.uri.queryParameters['isLandscape'] != null) {
            needLandscape = state.uri.queryParameters['isLandscape'] == "true";
          }
          if (needLandscape == null) {
            if (state.extra != null && state.extra is Map && (state.extra as Map)['isLandscape'] != null) {
              needLandscape = (state.extra as Map)['isLandscape'] == "true";
            }
          }
        }
        return CupertinoPage(child: routeMap[path]!.call(_, state), name: path, arguments: {
          "extra": state.extra,
          "queryParameters": state.uri.queryParameters,
          "isLandscape": needLandscape,
        });
      });
}
