// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/config/app_config.dart';
// import 'package:lib_base/config/route_utils.dart';
// import 'package:lib_base/config/theme_config.dart';
// import 'package:lib_base/providers/user/user_info_provider.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:yyb_flutter/src/generated/assets.dart';

// class DiscoverAppBar extends ConsumerWidget {
//   const DiscoverAppBar({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     var userInfo = ref.watch(userInfoNotifierProvider);
//     var userInfoNotifier = ref.read(userInfoNotifierProvider.notifier);
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Stack(
//           children: [
//             Row(
//               children: [
//                 ImageUtil.userHeader(
//                     imageUrl: userInfo.obj?.photo ?? "", size: 40.r),
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Text(
//                       userInfo.obj?.nickname ?? "",
//                       style: ThemeConfig.currentTheme.text15,
//                     ),
//                     Padding(
//                       padding: const EdgeInsets.only(top: 5.0),
//                       child: Text(
//                         "素养* 拓展",
//                         style: ThemeConfig.currentTheme.text13P1,
//                       ),
//                     ),
//                   ],
//                 )
//               ],
//             ),
//             //头像左上角会员角标
//             // Positioned(child: )
//           ],
//         ),
//         InkWell(
//           onTap: () {
//             toInteractWebviewUrlPage(
//               url: AppConfig.FZ_PURCHASED_ORDERS_URL,
//             );
//           },
//           child: Row(
//             children: [
//               Image.asset(
//                 Assets.imagesFzGwcIcon,
//                 width: 18.r,
//               ),
//               Padding(
//                 padding: EdgeInsets.only(left: 5.w),
//                 child: Text(
//                   "已购",
//                   style: ThemeConfig.currentTheme.text14P2,
//                 ),
//               )
//             ],
//           ),
//         )
//       ],
//     );
//   }
// }
