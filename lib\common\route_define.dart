import 'package:go_router/go_router.dart';
import 'package:lib_base/config/application.dart';
import 'package:lib_base/config/route_name.dart';
import 'package:lib_base/model/http/new_class_info_model.dart';
import 'package:yyb_auth/config/auth_router.dart' as authRouter;
import 'package:yyb_dubbing/config/router.dart' as dubbingRouter;
import 'package:yyb_flutter/model/http/head_info_model.dart';
import 'package:yyb_flutter/model/http/my_header_model.dart';
import 'package:yyb_flutter/pages/home/<USER>/english/pages/my_class/my_class_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/english/pages/my_class/pages/join_class_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/english/pages/my_class/pages/student_detail_info/student_detail_info_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/feedback/feedback_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/feedback/pages/feedback_list_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/help_and_support/help_and_support_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/manager_pwd/reset_pwd/reset_manager_pwd_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/my_point/my_point_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/my_wallet/my_wallet_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/setting/pages/about_us_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/setting/pages/account_security_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/setting/pages/notification_setting/notification_setting_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/setting/pages/update_phone/update_phone_new_phone_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/setting/pages/update_phone/update_phone_vcode_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/setting/pages/update_pwd_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/share_center/share_center_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/user_header/pages/exchanged_header_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/user_header/pages/header_card_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/user_header/pages/header_card_review_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/user_header/user_header_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/user_info/pages/book_version_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/user_info/pages/update_name_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/user_info/pages/update_school_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/user_info/user_info_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/sign_student/page/sign_calendar_page/sign_calendar_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/sign_student/sign_student_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/english/pages/switch_book/english_switch_book_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/expand/pages/short_video/pages/short_video_play_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/expand/pages/short_video/short_video_page.dart';
import 'package:yyb_flutter/pages/reg/complete_register/complete_register_page.dart';
import 'package:yyb_flutter/pages/reg/reg_book/reg_book_page.dart';
import 'package:yyb_flutter/pages/toolbar/message/message_page.dart';
import 'package:yyb_flutter/pages/toolbar/message/pages/message_details_page.dart';
import 'package:yyb_flutter/pages/toolbar/translate/translate_page.dart';
import 'package:yyb_word_remember/config/router.dart' as wordRememberRouter;
import 'package:yyb_word_dictation/config/router.dart' as wordDictationRouter;

import 'package:yyb_text_dubbing/config/router.dart' as textDubbingRouter;
import 'package:yyb_picture_book/config/router.dart' as pictureBookRouter;
import 'package:yyb_course_animation/config/router.dart'
    as courseAnimationRouter;
import 'package:yyb_interactive_reading/config/router.dart'
    as interactiveReadingRouter;
import 'package:yyb_happy_player/config/router.dart' as happyPlayerRouter;
import 'package:yyb_happy_listening_time/config/router.dart'
    as happyListeningRouter;
import 'package:yyb_recite_the_text/config/router.dart' as reciteTheTextRouter;
import 'package:yyb_listen_course/config/router.dart' as listenCourseRouter;
import 'package:yyb_flutter/pages/home/<USER>/vip/vip_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/math/pages/chinese/pages/switch_book/switch_book_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/my_order/my_order_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/setting/setting_page.dart';
import 'package:yyb_flutter/pages/reg/reg_school/reg_school_page.dart';
import 'package:lib_base/model/student_detail_param.dart';
import 'package:yyb_speech_evaluating/config/router.dart'
    as speechEvaluatingRouter;
import 'package:lib_base/model/feedback_page_param.dart';

import '../pages/guide/guid_page.dart';
import '../pages/home/<USER>';
import '../pages/home/<USER>/math/pages/math/pages/switch_book/switch_book_page.dart';
import '../pages/reg/reg_gradle/reg_gradle_page.dart';
import '../pages/splash/splash_page.dart';
import 'package:yyb_practice_work/config/router.dart' as myPracticeWorkRouter;
import 'package:yyb_daily_sentence/config/router.dart'
    as dailySentenceRouter; //
import 'package:lib_base/config/router_manager.dart';
import 'package:yyb_unit_synchronised_learning/config/router.dart'
    as unitSynchronisedLearningRouter; //
import 'package:yyb_knowledge_clearance/config/router.dart'
    as knowledgeClearanceHome;
import 'package:yyb_bilingual_reading/config/router.dart'
    as bilingualReadingRouter;
import 'package:yyb_interest_exercises/config/router.dart'
    as interestExercisesRouter;
import 'package:yyb_grammar_explanation/config/router.dart'
    as grammarExplanationRouter;
import 'package:yyb_basic_training/config/router.dart' as basicTrainingRouter;
import 'package:yyb_manager_password/config/router.dart'
    as managerPasswordRouter;
import 'package:yyb_brain_word_memory/config/router.dart'
    as brainWordMemoryRouter;
import 'package:yyb_intensive_listening/config/router.dart'
    as intensiveListeningRouter;
import 'package:yyb_reading_beautiful_prose/config/router.dart'
    as readingBeautifulProseRouter;
import 'package:yyb_grammer_one_point_pass/config/router.dart'
    as grammerOnePointPassRouter;
import 'package:yyb_pinyin_street/config/router.dart' as pinyinStreetRouter;

//  import 'package:ai_intelligent_flutter/config/route/route_name.dart'
// as aiIntelligentRouter;
import 'package:yyb_unit_integrated_learning/config/router.dart'
    as unitIntegratedLearningRouter;

import 'package:yyb_text_reading_points/config/router.dart'
    as textReadingPoints;

import 'package:yyb_knowledge_animation/config/router.dart' as knowledgeAnimationRouter;
import 'package:yyb_step_read/config/router.dart' as stepReadRouter;
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/my_favorite/my_favorite_page.dart';
import 'package:yyb_flutter/pages/home/<USER>/mine/pages/my_favorite/page/my_favorite_detail_page.dart';

class ERouteName {
  static String regSchool = "regSchool";
  static String englishSwitchBook = "switchEnglishBook";
  static String chineseSwitchBook = "chineseSwitchBook";
  static String mathSwitchBook = "mathSwitchBook";
  static String setting = "setting";
  static String shortVideoPlayer = "shortVideoPlayer";
  static String myOrder = "myOrder";
  static String regBook = "regBook";
  static String completeRegister = "completeRegister";
  static String updateName = "updateName";
  static String updateSchool = "updateSchool";
  static String bookVersion = "bookVersion";
  static String myWallet = "myWallet";
  static String feedbackList = "feedbackList";
  static String accountSecurity = "accountSecurity";
  static String aboutUs = "aboutUs";
  static String updatePwd = "updatePwd";
  static String updatePhoneVcode = "updatePhoneVcode";
  static String updatePhoneOfNewPhone = "updatePhoneOfNewPhone";
  static String userHeader = "userHeader";
  static String headerCard = "headerCard";
  static String exchangedHeader = "exchangedHeader";
  static String headerCardReview = "headerCardReview";
  static String studentDetailInfo = "studentDetailInfo";
  static String notificationSetting = "notificationSetting";
  static String myMessage = "myMessage";
  static String translate = "translate";
  static String messageDetails = "messageDetails";
  static String dailySentence = "dailySentence";
  static String unitSynchronisedLearning = "unitSynchronisedLearning";
  static String shortVideoPage = "/shortVideoPage";
  static String signCalendarPage = "signCalendarPage"; // 学生签到日历
  static String shareWithGift = "shareWithGift"; // 分享有礼
}

final Map<String, GoRouterWidgetBuilder> routeBuilderMap = {
  RouteName.splash: (_, state) => const SplashPage(),
  RouteName.home: (_, state) => const HomePage(),
  RouteName.guid: (_, state) => const GuidPage(),
  RouteName.regGradle: (_, state) => const RegGradlePage(),
  ERouteName.regSchool: (_, state) {
    String? gradeIndex = state.uri.queryParameters['gradeIndex'];
    String? gradeName = state.uri.queryParameters['gradeName'];
    return RegSchoolPage(gradeIndex: gradeIndex, gradeName: gradeName);
  },
  ERouteName.englishSwitchBook: (_, state) {
    //显示初中
    bool showJuniorVersion=(state.uri.queryParameters['showJuniorVersion']?.toString()??'true') == 'true';
  //显示小学
    bool? showGradeVersion=(state.uri.queryParameters['showGradeVersion']?.toString()??'true') == 'true';
    return EnglishSwitchBookPage(showGradeVersion: showGradeVersion,showJuniorVersion: showJuniorVersion,);
  },
  ERouteName.chineseSwitchBook: (_, state) => const ChineseSwitchBookPage(),
  ERouteName.mathSwitchBook: (_, state) => const MathSwitchBookPage(),
  ERouteName.setting: (_, state) => const SettingPage(),
  ERouteName.shortVideoPlayer: (_, state) {
    ShortVideoPlayPageParam extra = state.extra as ShortVideoPlayPageParam;
    return ShortVideoPlayPage(
      param: extra,
    );
  },
  ERouteName.myMessage: (_, state) {
    return MessagePage();
  },
  ERouteName.messageDetails: (_, state) {
    MessageDetailsParam extra = state.extra as MessageDetailsParam;
    return MessageDetailsPage(
      param: extra,
    );
  },
  ERouteName.myOrder: (_, state) => const MyOrderPage(),
  RouteName.myVip: (_, state) => VipPage(
        showBack: true,
      ),
  RouteName.myFavorite: (_, state) => const MyFavoritePage(),
  ERouteName.regBook: (_, state) {
    String? gradeIndex = state.uri.queryParameters['gradeIndex'];
    String? schoolId = state.uri.queryParameters['schoolId'];
    String? areaCode = state.uri.queryParameters['areaCode'];
    String? gradeName = state.uri.queryParameters['gradeName'];
    return RegBookPage(
      gradeIndex: gradeIndex,
      schoolId: schoolId,
      areaCode: areaCode,
      gradeName: gradeName,
    );
  },
  ERouteName.completeRegister: (_, state) {
    return CompleteRegisterPage();
  },
  RouteName.userInfo: (_, state) {
    return UserInfoPage();
  },
  ERouteName.updateName: (_, state) {
    return UpdateNamePage();
  },
  ERouteName.updateSchool: (_, state) {
    return UpdateSchoolPage();
  },
  ERouteName.bookVersion: (_, state) {
    String subject = state.uri.queryParameters['subject']!;
    return BookVersionPage(
      subject: subject,
    );
  },
  RouteName.myPoint: (_, state) {
    return MyPointPage();
  },
  ERouteName.myWallet: (_, state) {
    return MyWalletPage();
  },
  RouteName.helpAndSupport: (_, state) {
    return HelpAndSupportPage();
  },
  RouteName.feedback: (_, state) {
    FeedbackPageParam? param = state.extra as FeedbackPageParam?;
    return FeedbackPage(
      param: param,
    );
  },
  ERouteName.feedbackList: (_, state) {
    return FeedbackListPage();
  },
  ERouteName.accountSecurity: (_, state) {
    return AccountSecurityPage();
  },
  ERouteName.aboutUs: (_, state) {
    return AboutUsPage();
  },
  ERouteName.updatePwd: (_, state) {
    return UpdatePwdPage();
  },
  ERouteName.updatePhoneVcode: (_, state) {
    return UpdatePhoneOfVCodePage();
  },
  ERouteName.updatePhoneOfNewPhone: (_, state) {
    return UpdatePhoneOfNewPhonePage();
  },
  RouteName.myClass: (_, state) {
    bool showJoin = state.uri.queryParameters['showJoin'] == 'true';
    return MyClassPage(
      showJoin: showJoin,
    );
  },
  RouteName.joinClass: (_, state) {
    NewClassInfoModel model = state.extra as NewClassInfoModel;
    return JoinClassPage(model: model);
  },
  ERouteName.userHeader: (_, state) {
    String index = state.uri.queryParameters['index'] ?? "0";
    return UserHeaderPage(
      index: int.tryParse(index) ?? 0,
    );
  },
  ERouteName.exchangedHeader: (_, state) {
    return ExchangedHeaderPage();
  },
  ERouteName.headerCard: (_, state) {
    List<HeaderImageInfo> cards = state.extra as List<HeaderImageInfo>;
    String index = state.uri.queryParameters['index'] ?? "0";
    return HeaderCardPage(
      cards: cards,
      index: int.parse(index),
    );
  },
  ERouteName.headerCardReview: (_, state) {
    MyHeaderModel model = state.extra as MyHeaderModel;
    return HeaderCardReviewPage(
      model: model,
    );
  },
  ERouteName.studentDetailInfo: (_, state) {
    StudentDetailParam param = state.extra as StudentDetailParam;
    return StudentDetailInfoPage(
      param: param,
    );
  },
  ERouteName.notificationSetting: (_, state) {
    return NotificationSettingPage();
  },
  RouteName.resetManagerPwd: (_, state) => ResetManagerPwdPage(),
  ERouteName.translate: (_, state) {
    return TranslatePage();
  },
  ERouteName.shortVideoPage: (_, state) {
    ShortVideoPageParam param = state.extra as ShortVideoPageParam;
    return ShortVideoPage(
      param: param,
    );
  },
  RouteName.signStudentPage: (_, state) {
    return SignStudentPage();
  },
  ERouteName.signCalendarPage: (_, state) {
    return SignCalendarPage();
  },
  ERouteName.shareWithGift: (_, state) {
    return ShareCenterPage();
  },
};

final List<RouteBase> routeBases = [
  _goRoute(RouteName.home, routes: [
    _goRoute(ERouteName.englishSwitchBook, routes: [
      _goRoute(ERouteName.shortVideoPlayer),
    ]),
    _goRoute(ERouteName.chineseSwitchBook),
    _goRoute(ERouteName.mathSwitchBook),
    _goRoute(ERouteName.setting, routes: [
      _goRoute(ERouteName.accountSecurity, routes: [
        _goRoute(ERouteName.updatePwd),
        _goRoute(ERouteName.updatePhoneVcode,
            routes: [_goRoute(ERouteName.updatePhoneOfNewPhone)]),
      ]),
      _goRoute(ERouteName.aboutUs),
      _goRoute(ERouteName.notificationSetting),
    ]),
    _goRoute(ERouteName.myMessage, routes: [
      _goRoute(ERouteName.messageDetails),
    ]),
    _goRoute(ERouteName.translate),
    _goRoute(ERouteName.myOrder),
    _goRoute(RouteName.resetManagerPwd),
    _goRoute(ERouteName.shareWithGift),
    _goRoute(RouteName.userInfo, routes: [
      _goRoute(ERouteName.updateName),
      _goRoute(ERouteName.updateSchool),
      _goRoute(ERouteName.bookVersion),
    ]),
    _goRoute(RouteName.myPoint, routes: [
      _goRoute(ERouteName.userHeader, routes: [
        _goRoute(ERouteName.headerCard),
        _goRoute(ERouteName.exchangedHeader,
            routes: [_goRoute(ERouteName.headerCardReview)]),
      ])
    ]),
    _goRoute(
      ERouteName.myWallet,
    ),
    _goRoute(RouteName.myFavorite),
    _goRoute(RouteName.helpAndSupport, routes: [
      _goRoute(RouteName.feedback, routes: [_goRoute(ERouteName.feedbackList)]),
    ]),
    _goRoute(RouteName.myClass, routes: [
      _goRoute(RouteName.joinClass),
      _goRoute(ERouteName.studentDetailInfo),
    ])
  ]),
  _goRoute(RouteName.splash),
  _goRoute(RouteName.myVip),
  _goRoute(RouteName.guid),
  _goRoute(ERouteName.shortVideoPage),
  _goRoute(RouteName.regGradle, routes: [
    _goRoute(ERouteName.regSchool, routes: [
      _goRoute(ERouteName.regBook),
      _goRoute(ERouteName.completeRegister),
    ]),
  ]),
  _goRoute(RouteName.signStudentPage,
      routes: [_goRoute(ERouteName.signCalendarPage)]),
];

GoRoute _goRoute(String path, {List<GoRoute>? routes}) {
  return goRoute(path, routeBuilderMap, routes: routes);
}

final routerDefine = _routerDefine();

GoRouter _routerDefine() {
  List<RouteBase> routes = [
    ...routeBases,
    ...baseRoutes,
    ...authRouter.routeBases,
    ...dubbingRouter.routeBases,
    ...wordRememberRouter.routeBases,
    ...wordDictationRouter.routeBases,
    ...textDubbingRouter.routeBases,
    ...pictureBookRouter.routeBases,
    ...courseAnimationRouter.routeBases,
    ...listenCourseRouter.routeBases,
    ...interactiveReadingRouter.routeBases,
    ...happyPlayerRouter.routeBases,
    ...speechEvaluatingRouter.routeBases,
    ...myPracticeWorkRouter.routeBases,
    ...reciteTheTextRouter.routeBases,
    ...knowledgeClearanceHome.routeBases,
    ...bilingualReadingRouter.routeBases,
    ...interestExercisesRouter.routeBases,
    ...grammarExplanationRouter.routeBases,
    ...basicTrainingRouter.routeBases,
    ...dailySentenceRouter.routeBases,
    ...grammerOnePointPassRouter.routeBases,
    ...pinyinStreetRouter.routeBases,
    ...unitSynchronisedLearningRouter.routeBases,
    ...managerPasswordRouter.routeBases,
    ...brainWordMemoryRouter.routeBases,
    ...happyListeningRouter.routeBases,
    // ...aiIntelligentRouter.baseRoutes,
    ...intensiveListeningRouter.routeBases,
    ...readingBeautifulProseRouter.routeBases,
    ...textReadingPoints.routeBases,
    ...unitIntegratedLearningRouter.routeBases,
    ...knowledgeAnimationRouter.routeBases,
    ...stepReadRouter.routeBases,
  ];
  return GoRouter(
    navigatorKey: MApplication.navigatorKey,
    observers: [routeObserver],
    debugLogDiagnostics: false,
    initialLocation: RouteName.splash,
    routes: routes,
  );
}
