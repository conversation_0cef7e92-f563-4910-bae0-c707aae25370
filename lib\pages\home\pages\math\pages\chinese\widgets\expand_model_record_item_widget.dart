// import 'dart:convert';

// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/config/theme_config.dart';
// import 'package:lib_base/log/log.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:lib_base/widgets/image/net_cache_image.dart';
// import 'package:yyb_flutter/src/generated/assets.dart';
// import 'package:yyb_flutter/model/http/query_chinese_expand_model.dart';

// class ExpandModelRecordCard extends StatelessWidget {
//   final RecordedType type;
//   const ExpandModelRecordCard({super.key, required this.type});

//   @override
//   Widget build(BuildContext context) {
//     List<Widget> children = [];
//     List<ModuleVos> modules = type.moduleVOS ?? [];
//     if (modules.isNotEmpty) {
//       for (ModuleVos v in modules) {
//         children.add(ExpandModelRecordItem(
//           v: v,
//         ));
//       }
//       return Container(
//         margin: EdgeInsets.only(bottom: 15.h),
//         padding: EdgeInsets.symmetric(horizontal: 13.w, vertical: 17.h),
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(10.r),
//           color: Colors.white,
//         ),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Padding(
//               padding: EdgeInsets.only(bottom: 10.h),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   Image.asset(
//                     Assets.imagesIconArrowDividerLeft,
//                     width: 76.42.w,
//                   ),
//                   Padding(
//                     padding: EdgeInsets.symmetric(horizontal: 13.5.w),
//                     child: Text(
//                       type.typeName ?? "",
//                       style: ThemeConfig.currentTheme.text17,
//                     ),
//                   ),
//                   Image.asset(
//                     Assets.imagesIconArrowDividerRight,
//                     width: 76.42.w,
//                   ),
//                 ],
//               ),
//             ),
//             Wrap(
//               children: children,
//               spacing: 15.w,
//               runSpacing: 15.h,
//             )
//           ],
//         ),
//       );
//     } else {
//       return SizedBox.shrink();
//     }
//   }
// }

// class ExpandModelRecordItem extends StatelessWidget {
//   final ModuleVos v;
//   const ExpandModelRecordItem({super.key, required this.v});

//   @override
//   Widget build(BuildContext context) {
//     String image = v.image ?? v.imageUrl ?? v.stuIcon ?? "";
//     if (image.isEmpty) {
//       Logger.info("image 为空:${jsonEncode(v.toJson())}");
//     }
//     String imageUrl = ImageUtil.getImageUrl(image);
//     String name = v.name ?? "";
//     return Container(
//       width: (1.sw - 30.w - 30.w - 26.w) / 2,
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           BaseNetCacheImage(
//             imageUrl: imageUrl,
//             width: 152.5.r,
//             height: 91.92.r,
//           ),
//           Padding(
//             padding: EdgeInsets.only(top: 13.h),
//             child: Text(
//               name,
//               style: ThemeConfig.currentTheme.text14,
//             ),
//           )
//         ],
//       ),
//     );
//   }
// }
