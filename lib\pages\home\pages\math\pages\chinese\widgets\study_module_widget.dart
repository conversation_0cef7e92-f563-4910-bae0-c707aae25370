// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/config/theme_config.dart';
// import 'package:lib_base/model/http/book_study_record_vo.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:lib_base/widgets/image/net_cache_image.dart';

// class StudyModuleItemWidget extends StatelessWidget {
//   final BookModuleInfoList module;
//   final VoidCallback? onTap;
//   const StudyModuleItemWidget({super.key, required this.module, this.onTap});

//   @override
//   Widget build(BuildContext context) {
//     String image = module.stuIcon ?? "";
//     String imageUrl = ImageUtil.getImageUrl(image);
//     return InkWell(
//       onTap: onTap,
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Padding(
//             padding: EdgeInsets.only(bottom: 5.r),
//             child: BaseNetCacheImage(
//               imageUrl: imageUrl,
//               width: 40.r,
//               height: 40.r,
//             ),
//           ),
//           Text(
//             module.name ?? "",
//             style: ThemeConfig.currentTheme.text14,
//           ),
//         ],
//       ),
//     );
//   }
// }
