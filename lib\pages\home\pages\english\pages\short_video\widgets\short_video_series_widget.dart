// import 'package:card_swiper/card_swiper.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:lib_base/config/theme_config.dart';
// import 'package:lib_base/log/log.dart';
// import 'package:lib_base/widgets/button/simple_button.dart';
// import 'package:yyb_flutter/src/extention/build_context_ext.dart';
// import 'package:lib_base/model/id_name_info.dart';
// import 'package:yyb_flutter/providers/english/english_short_video_series_provider.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';

// class ShortVideoSeriesWidget extends ConsumerStatefulWidget {
//   final ValueChanged<String>? onTap;
//   const ShortVideoSeriesWidget({
//     super.key,
//     this.onTap,
//   });

//   @override
//   ConsumerState<ShortVideoSeriesWidget> createState() =>
//       _ShortVideoSeriesWidgetState();
// }

// class _ShortVideoSeriesWidgetState
//     extends ConsumerState<ShortVideoSeriesWidget> {
//   bool showAll = false;

//   int? checkedIndex;

//   @override
//   Widget build(BuildContext context) {
//     List<IdNameInfo> ids = ref.watch(englishShortVideoSeriesNotifierProvider);

//     return !showAll
//         ? OneLineRow(
//             ids: ids,
//             showAll: () {
//               if (!showAll) {
//                 showAll = true;
//                 setState(() {});
//               }
//             },
//             onTap: (IdNameInfo item) {
//               checkedIndex = ids.indexOf(item);
//               Logger.info("=== checkedIndex:$checkedIndex");
//               widget.onTap?.call(item.name ?? "");
//               setState(() {});
//             },
//             checkedIndex: checkedIndex,
//           )
//         : ShowAllRow(
//             ids: ids,
//             showOneLine: () {
//               if (showAll) {
//                 showAll = false;
//                 setState(() {});
//               }
//             },
//             onTap: (IdNameInfo item) {
//               checkedIndex = ids.indexOf(item);
//               widget.onTap?.call(item.name ?? "");
//               setState(() {});
//             },
//             checkedIndex: checkedIndex,
//           );
//   }
// }

// class ShowAllRow extends StatelessWidget {
//   final List<IdNameInfo> ids;
//   final ValueChanged<IdNameInfo>? onTap;
//   final GestureTapCallback showOneLine;
//   final int? checkedIndex;
//   const ShowAllRow(
//       {super.key,
//       required this.ids,
//       this.onTap,
//       required this.showOneLine,
//       this.checkedIndex});

//   @override
//   Widget build(BuildContext context) {
//     List<Widget> children = [];
//     var color = ThemeConfig.currentTheme.colorDesignPrimary3;
//     for (int i = 0; i < ids.length; i++) {
//       IdNameInfo item = ids[i];
//       children.add(SimpleButton(
//         text: item.name ?? "",
//         textStyle: ThemeConfig.currentTheme.text12
//             .copyWith(color: checkedIndex == i ? null : color),
//         width: 80.r,
//         boarderColor: color,
//         radius: 10.r,
//         onTap: () {
//           onTap?.call(item);
//         },
//         padding: EdgeInsets.symmetric(horizontal: 3.r, vertical: 3.r),
//         margin: EdgeInsets.symmetric(vertical: 10.h, horizontal: 2.r),
//       ));
//     }
//     return Padding(
//       padding: const EdgeInsets.only(top: 8, bottom: 8),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Wrap(
//             children: children,
//           ),
//           InkWell(
//             onTap: showOneLine,
//             child: Padding(
//               padding: const EdgeInsets.all(8.0),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   Text(
//                     context.string.textOfCollapse,
//                     style: ThemeConfig.currentTheme.text13P2,
//                   ),
//                   Icon(
//                     Icons.keyboard_arrow_up_outlined,
//                     color: ThemeConfig.currentTheme.colorTextPrimary1,
//                   )
//                 ],
//               ),
//             ),
//           )
//         ],
//       ),
//     );
//   }
// }

// class OneLineRow extends StatelessWidget {
//   final List<IdNameInfo> ids;
//   final ValueChanged<IdNameInfo>? onTap;
//   final GestureTapCallback showAll;
//   final int? checkedIndex;
//   OneLineRow(
//       {required this.ids,
//       this.onTap,
//       required this.showAll,
//       this.checkedIndex});

//   @override
//   Widget build(BuildContext context) {
//     List<Widget> children = [];
//     for (int i = 0; i < ids.length; i++) {
//       IdNameInfo id = ids[i];
//       children.add(OneLineText(
//         text: id.name ?? "",
//         isChecked: checkedIndex == i,
//         onTap: () {
//           onTap?.call(id);
//         },
//       ));
//     }
//     return Stack(
//       children: [
//         SingleChildScrollView(
//           scrollDirection: Axis.horizontal,
//           child: Container(
//             child: Row(
//               children: children,
//             ),
//           ),
//         ),
//         Positioned(
//             right: 0,
//             child: InkWell(
//               onTap: showAll,
//               child: Container(
//                 color: ThemeConfig.currentTheme.baseBackgroundColor,
//                 padding: const EdgeInsets.all(8.0),
//                 child: Icon(
//                   Icons.keyboard_arrow_down_outlined,
//                   color: ThemeConfig.currentTheme.colorDesignPrimary3,
//                 ),
//               ),
//             )),
//       ],
//     );
//   }
// }

// class OneLineText extends StatelessWidget {
//   final String text;
//   final bool isChecked;
//   final GestureTapCallback? onTap;
//   const OneLineText(
//       {super.key, required this.text, required this.isChecked, this.onTap});

//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: onTap,
//       child: Padding(
//         padding: const EdgeInsets.all(8.0),
//         child: Text(
//           text,
//           style: TextStyle(
//               fontSize: isChecked ? 18.sp : 14.sp,
//               color: isChecked
//                   ? ThemeConfig.currentTheme.colorTextPrimary3
//                   : ThemeConfig.currentTheme.colorDesignPrimary3),
//         ),
//       ),
//     );
//   }
// }
