import 'package:json_annotation/json_annotation.dart';

part 'expand_video_item.g.dart';

@JsonSerializable()
class ExpandVideoItem {
  String? id;
  String? saveFilePath;
  int? downLoadId;
  int? sqlId;
  int? downLoadProgress;
  int? downLoadStatus;
  String? coverImage;
  String? name;
  String? msg;
  bool? needUpdate;
  int? createDate;
  int? updateDate;
  String? subName;
  String? bgkImg;
  String? playTimes;
  String? themeId;
  String? videoPath;
  String? videoSimplePath;
  bool? collectStatus;
  int? sort;
  int? resourceFrom;
  String? videoSourcePath;
  String? playTimesZh;
  String? remarks;
  int? time;
  int? videoSize;
  String? videoDesc;
  String? shareType;
  String? expModuleId;

  ExpandVideoItem({
    this.id,
    this.saveFilePath,
    this.downLoadId,
    this.sqlId,
    this.downLoadProgress,
    this.downLoadStatus,
    this.coverImage,
    this.name,
    this.msg,
    this.needUpdate,
    this.createDate,
    this.updateDate,
    this.subName,
    this.bgkImg,
    this.playTimes,
    this.themeId,
    this.videoPath,
    this.videoSimplePath,
    this.collectStatus,
    this.sort,
    this.resourceFrom,
    this.videoSourcePath,
    this.playTimesZh,
    this.remarks,
    this.time,
    this.videoSize,
    this.videoDesc,
    this.shareType,
    this.expModuleId,
  });

  factory ExpandVideoItem.fromJson(Map<String, dynamic> json) =>
      _$ExpandVideoItemFromJson(json);

  Map<String, dynamic> toJson() => _$ExpandVideoItemToJson(this);
}