import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/widgets/common/base_app_bar.dart';
import 'package:lib_base/widgets/common/base_scaffold.dart';

class MyFavoritePage extends ConsumerStatefulWidget {
  const MyFavoritePage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _MyFavoritePageState();
}

class _MyFavoritePageState extends ConsumerState<MyFavoritePage> {
  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      appBar: BaseAppBar.normalAppBar(titleText: "我的收藏"),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 收藏内容列表
            SizedBox(height: 20.h),
            Text('我的收藏内容'),
          ],
        ),
      ),
    );
  }
}