// import 'dart:math';

// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/config/route_utils.dart';
// import 'package:lib_base/config/theme_config.dart';
// import 'package:lib_base/providers/books/chinese/chinese_book_info_provider.dart';
// import 'package:lib_base/providers/books/math/math_book_info_provider.dart';
// import 'package:yyb_flutter/common/route_define.dart';
// import 'package:lib_base/providers/books/english/english_book_info_provider.dart';
// import 'package:yyb_flutter/src/extention/build_context_ext.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:yyb_flutter/providers/chinese_math_page_tab_provider.dart';

// class ChineseMathAppBar extends StatelessWidget {
//   const ChineseMathAppBar({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         ChineseAndMathTabWidget(),
//         _rightHeader(context),
//       ],
//     );
//   }

//   Widget _rightHeader(BuildContext context) {
//     var padding = EdgeInsets.only(right: 16.r);
//     return Consumer(
//       builder: (BuildContext context, WidgetRef ref, Widget? child) {
//         int index = ref.watch(chineseMathPageTabNotifierProvider);
//         MMBookInfo? bookInfo = ref.watch(index == 0
//             ? chineseBookInfoProviderProvider
//             : mathBookInfoProviderProvider);
//         return InkWell(
//           onTap: () {
//             toPage(index == 0
//                 ? ERouteName.chineseSwitchBook
//                 : ERouteName.mathSwitchBook);
//           },
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Row(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Text(
//                     bookInfo != null
//                         ? "${bookInfo.gradeStr ?? ''}${bookInfo.fasciculeStr ?? ''}"
//                         : context.string.chooseTecMaterial,
//                     style: TextStyle(
//                         fontSize: 14.sp,
//                         color: const Color(0xff323232),
//                         fontWeight: FontWeight.bold),
//                     textAlign: TextAlign.start,
//                   ),
//                   Column(
//                     children: [
//                       SizedBox(
//                         height: 7.sp,
//                       ),
//                       //旋转
//                       Transform.rotate(
//                         angle: pi / 4,
//                         child: Icon(
//                           Icons.arrow_right,
//                           size: 16.sp,
//                           color: const Color(0xffCCCCCC),
//                         ),
//                       )
//                     ],
//                   )
//                 ],
//               ),
//               bookInfo != null
//                   ? Text(
//                       bookInfo.versionStr ?? "",
//                       style: TextStyle(
//                           fontSize: 10.sp,
//                           color: ThemeConfig.currentTheme.colorTextPrimary1),
//                     )
//                   : SizedBox.shrink(),
//             ],
//           ),
//         );
//       },
//     );
//   }
// }

// class ChineseAndMathTabWidget extends ConsumerWidget {
//   const ChineseAndMathTabWidget({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     int index = ref.watch(chineseMathPageTabNotifierProvider);
//     var notifier = ref.read(chineseMathPageTabNotifierProvider.notifier);
//     var chosenStyle = TextStyle(
//         fontSize: 24.sp,
//         fontWeight: FontWeight.bold,
//         color: ThemeConfig.currentTheme.colorTextPrimary3);
//     var unchosenStyle = TextStyle(
//         fontSize: 19.sp, color: ThemeConfig.currentTheme.colorTextPrimary3);
//     return Row(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         InkWell(
//           onTap: () {
//             notifier.jumpTo(0);
//           },
//           child: Padding(
//             padding: EdgeInsets.only(right: 10.w),
//             child: Text(
//               context.string.textOfChinese,
//               style: index == 0 ? chosenStyle : unchosenStyle,
//             ),
//           ),
//         ),
//         InkWell(
//           onTap: () {
//             notifier.jumpTo(1);
//           },
//           child: Padding(
//             padding: EdgeInsets.only(left: 10.w),
//             child: Text(
//               context.string.textOfMath,
//               style: index == 1 ? chosenStyle : unchosenStyle,
//             ),
//           ),
//         )
//       ],
//     );
//   }
// }
