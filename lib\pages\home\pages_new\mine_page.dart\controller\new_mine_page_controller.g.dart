// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'new_mine_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$newMinePageControllerHash() =>
    r'cce305c113b52655f7174f4bc2f3a7be282efe49';

/// See also [NewMinePageController].
@ProviderFor(NewMinePageController)
final newMinePageControllerProvider =
    AutoDisposeNotifierProvider<NewMinePageController, int>.internal(
  NewMinePageController.new,
  name: r'newMinePageControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$newMinePageControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NewMinePageController = AutoDisposeNotifier<int>;
// ignore_for_file: unnecessary_raw_strings, subtype_of_sealed_class, invalid_use_of_internal_member, do_not_use_environment, prefer_const_constructors, public_member_api_docs, avoid_private_typedef_functions
