// import 'package:flutter/cupertino.dart';
// import 'package:lib_base/config/theme_config.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/widgets/business/title_row/title_row_item.dart';
// import 'package:yyb_flutter/src/generated/assets.dart';

// class SwitchRowButton extends StatelessWidget {
//   final String title;
//   final bool hasNew;
//   final String hintText;
//   const SwitchRowButton(
//       {super.key,
//       required this.title,
//       required this.hasNew,
//       required this.hintText});

//   @override
//   Widget build(BuildContext context) {
//     return TitleRowItem(
//       title: title,
//       titleIcon: hasNew
//           ? Padding(
//               padding: EdgeInsets.only(left: 3.r),
//               child: Image.asset(
//                 Assets.imagesIconNew,
//                 width: 16.r,
//               ),
//             )
//           : null,
//       tail: Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Image.asset(
//             Assets.imagesIconSwitcher,
//             width: 12.4.r,
//           ),
//           Padding(
//             padding: EdgeInsets.only(left: 10.r),
//             child: Text(
//               hintText,
//               style: TextStyle(
//                   fontSize: 15.sp,
//                   color: ThemeConfig.currentTheme.colorTextPrimary2),
//             ),
//           )
//         ],
//       ),
//     );
//   }
// }
