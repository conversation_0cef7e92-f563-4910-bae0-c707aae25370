// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sign_task_view.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$signTaskViewControllerHash() =>
    r'66e955b8ed3320806d6ef9a610bdb20ec6145cd1';

/// See also [SignTaskViewController].
@ProviderFor(SignTaskViewController)
final signTaskViewControllerProvider =
    AutoDisposeNotifierProvider<SignTaskViewController, int>.internal(
  SignTaskViewController.new,
  name: r'signTaskViewControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$signTaskViewControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SignTaskViewController = AutoDisposeNotifier<int>;
// ignore_for_file: unnecessary_raw_strings, subtype_of_sealed_class, invalid_use_of_internal_member, do_not_use_environment, prefer_const_constructors, public_member_api_docs, avoid_private_typedef_functions
