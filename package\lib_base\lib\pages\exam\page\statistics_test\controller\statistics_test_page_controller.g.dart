// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'statistics_test_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$statisticsTestPageControllerHash() =>
    r'413a3d51838ac17ef940d62e2ff344e41a5e6b3c';

/// See also [StatisticsTestPageController].
@ProviderFor(StatisticsTestPageController)
final statisticsTestPageControllerProvider =
    AutoDisposeNotifierProvider<StatisticsTestPageController, int>.internal(
  StatisticsTestPageController.new,
  name: r'statisticsTestPageControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$statisticsTestPageControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$StatisticsTestPageController = AutoDisposeNotifier<int>;
// ignore_for_file: unnecessary_raw_strings, subtype_of_sealed_class, invalid_use_of_internal_member, do_not_use_environment, prefer_const_constructors, public_member_api_docs, avoid_private_typedef_functions
