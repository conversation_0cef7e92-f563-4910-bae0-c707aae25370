// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wrong_exercise_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$wrongExercisePageControllerHash() =>
    r'3d683b606b661a5143485c2be7638c6ea6d9e26c';

/// See also [WrongExercisePageController].
@ProviderFor(WrongExercisePageController)
final wrongExercisePageControllerProvider =
    AutoDisposeNotifierProvider<WrongExercisePageController, int>.internal(
  WrongExercisePageController.new,
  name: r'wrongExercisePageControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$wrongExercisePageControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WrongExercisePageController = AutoDisposeNotifier<int>;
// ignore_for_file: unnecessary_raw_strings, subtype_of_sealed_class, invalid_use_of_internal_member, do_not_use_environment, prefer_const_constructors, public_member_api_docs, avoid_private_typedef_functions
