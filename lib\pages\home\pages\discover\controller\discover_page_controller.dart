// import 'package:lib_base/model/http/ad_info.dart';
// import 'package:lib_base/providers/user/user_info_provider.dart';
// import 'package:lib_base/utils/business/ad_util.dart';
// import 'package:lib_base/widgets/refresh/refresh_view_mixin.dart';
// import 'package:riverpod_annotation/riverpod_annotation.dart';
// import 'package:yyb_flutter/model/http/discover_data_model.dart';
// import 'package:yyb_flutter/model/http/order_campandju_model.dart';
// import 'package:yyb_flutter/src/api/api_repository.dart';

// part 'discover_page_controller.g.dart';

// @riverpod
// class DiscoverPageController extends _$DiscoverPageController
//     with RefreshViewMixin {
//   Map<String, DiscoverDataModel> pageDataMap = {};
//   List<AdInfo> ads = [];

//   //购买的产品
//   List<OrderCampandjuModel> orderCampandjus = [];

//   String get bannerAdPostionId {
//     return AdUtil.getAdPositionId("faxian");
//   }

//   int build() {
//     return 0;
//   }

//   @override
//   Future fetchData() async {
//     List<Future> tasks = [..._loadTasks()];
//     return Future.wait(tasks);
//   }

//   List<Future> _loadTasks() {
//     var userInfo = ref.read(userInfoNotifierProvider);
//     return [
//       ApiRepository.discovereddata(
//               userId: userInfo.userId, grade: userInfo.gradeId)
//           .then((value) {
//         if (value.isSuccess && value.isDataNotNull) {
//           pageDataMap.clear();
//           value.dataNotNull.forEach((element) {
//             pageDataMap[element.id ?? ""] = element;
//           });
//         }
//       }),
//       ApiRepository.listavailables(
//               positionIds: [bannerAdPostionId], userId: userInfo.userId)
//           .then((response) {
//         if (response.isSuccess && response.isDataNotNull) {
//           ads = response.dataNotNull;
//         }
//       }),
//       ApiRepository.listordercampandju(userId: userInfo.userId).then((value) {
//         if (value.isSuccess && value.isDataNotNull) {
//           orderCampandjus = value.dataNotNull;
//         }
//       })
//     ];
//   }

//   List<AdInfo> getPageBannerAds() {
//     return ads;
//   }

//   DiscoverDataModel? get smallMenu => pageDataMap['smallmenu'];

//   DiscoverDataModel? get zb => pageDataMap['zb'];
//   //训练营
//   DiscoverDataModel? get xly => pageDataMap['xly'];
//   //得到故事
//   DiscoverDataModel? get sndd => pageDataMap['sndd'];
//   //教辅
//   DiscoverDataModel? get jf => pageDataMap['jf'];
// }
