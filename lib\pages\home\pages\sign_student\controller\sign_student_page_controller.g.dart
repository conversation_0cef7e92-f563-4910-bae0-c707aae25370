// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sign_student_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$signStudentPageControllerHash() =>
    r'5aa5416e7d2df57bfdfec10dddd4db674eab3c10';

/// See also [SignStudentPageController].
@ProviderFor(SignStudentPageController)
final signStudentPageControllerProvider = AutoDisposeNotifierProvider<
    SignStudentPageController, GetSignConfigModel?>.internal(
  SignStudentPageController.new,
  name: r'signStudentPageControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$signStudentPageControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SignStudentPageController = AutoDisposeNotifier<GetSignConfigModel?>;
// ignore_for_file: unnecessary_raw_strings, subtype_of_sealed_class, invalid_use_of_internal_member, do_not_use_environment, prefer_const_constructors, public_member_api_docs, avoid_private_typedef_functions
