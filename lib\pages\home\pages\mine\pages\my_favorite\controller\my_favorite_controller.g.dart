// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_favorite_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$myFavoriteControllerHash() =>
    r'9d8444394f344b0091640e690836d4545ebd0812';

/// See also [MyFavoriteController].
@ProviderFor(MyFavoriteController)
final myFavoriteControllerProvider =
    AutoDisposeAsyncNotifierProvider<MyFavoriteController, void>.internal(
  MyFavoriteController.new,
  name: r'myFavoriteControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$myFavoriteControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MyFavoriteController = AutoDisposeAsyncNotifier<void>;
// ignore_for_file: unnecessary_raw_strings, subtype_of_sealed_class, invalid_use_of_internal_member, do_not_use_environment, prefer_const_constructors, public_member_api_docs, avoid_private_typedef_functions
