// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/config/app_config.dart';
// import 'package:lib_base/providers/user/user_info_provider.dart';
// import 'package:lib_base/widgets/common/base_app_bar.dart';
// import 'package:lib_base/widgets/common/base_scaffold.dart';
// import 'package:lib_base/widgets/refresh/refresh_view.dart';
// import 'package:yyb_flutter/pages/home/<USER>/discover/controller/discover_page_controller.dart';
// import 'package:yyb_flutter/pages/home/<USER>/discover/widgets/discover_app_bar.dart';
// import 'package:yyb_flutter/pages/home/<USER>/pccompanying_learning/widget/jf_item_card.dart';
// import 'package:yyb_flutter/pages/home/<USER>/pccompanying_learning/widget/order_Item_card.dart';
// import 'package:yyb_flutter/pages/home/<USER>/pccompanying_learning/widget/small_menu_item.dart';
// import 'package:yyb_flutter/pages/home/<USER>/pccompanying_learning/widget/sndd_item_card.dart';
// import 'package:yyb_flutter/pages/home/<USER>/pccompanying_learning/widget/xly_item_card.dart';
// import 'package:yyb_flutter/pages/home/<USER>/pccompanying_learning/widget/zb_item_card.dart';
// import 'package:yyb_flutter/pages/home/<USER>/widget/home_ad_banner.dart';

// class DiscoverPage extends ConsumerWidget {
//   const DiscoverPage({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     DiscoverPageController controller =
//         ref.watch(discoverPageControllerProvider.notifier);
//     return Padding(
//       padding: EdgeInsets.only(top: 18.0.r),
//       child: BaseScaffold(
//         appBar: BaseAppBar.customAppBar(
//           title: DiscoverAppBar(),
//           toolbarHeight: 40.r,
//         ),
//         body: Padding(
//           padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 5.h),
//           child: RefreshView(
//             controller: controller,
//             sliversBuilder: (_) {
//               return [
//                 //banner广告
//                 SliverToBoxAdapter(
//                   child: HomeAdBanner(
//                     ads: controller.getPageBannerAds(),
//                   ),
//                 ),
//                 ..._children(controller, ref),
//               ];
//             },
//           ),
//         ),
//       ),
//     );
//   }

//   List<Widget> _children(DiscoverPageController controller, WidgetRef ref) {
//     List<Widget> children = [];
//     if (controller.smallMenu != null) {
//       //训练营， 得到故事，教辅
//       children.add(SmallMenuItemRow(
//         model: controller.smallMenu!,
//         gradeId: ref.read(userInfoNotifierProvider).gradeId,
//       ));
//     }
//     //已购资源
//     if (controller.orderCampandjus.isNotEmpty) {
//       children.add(OrderItems(orderCampandjus: controller.orderCampandjus));
//     }

//     if (controller.zb != null) {
//       //直播
//       // 直播是微信直播， 鸿蒙微信相关功能先屏蔽，
//       if (!AppConfig.isNeedHideForUnfinished) {
//         children.add(ZbItemCard(
//           model: controller.zb!,
//         ));
//       }
//     }

//     if (controller.xly != null) {
//       //训练营
//       children.add(XlyItemCard(
//         model: controller.xly!,
//       ));
//     }
//     if (controller.sndd != null) {
//       //得到故事
//       children.add(SnddItemCard(
//         model: controller.sndd!,
//       ));
//     }
//     if (controller.jf != null) {
//       //教辅
//       children.add(JfItemCard(
//         model: controller.jf!,
//       ));
//     }
//     return children;
//   }
// }
