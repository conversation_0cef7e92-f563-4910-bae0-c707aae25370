// import 'package:card_swiper/card_swiper.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:lib_base/config/application.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:lib_base/widgets/video/short_video_player/short_video_player.dart';
// import 'package:yyb_flutter/model/http/english_short_video_info.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:yyb_flutter/src/api/api_repository.dart';

// class ShortVideoPageParam {
//   int currentIndex;
//   List<ListObj> objs;
//   String? keywords;
//   int page;

//   ShortVideoPageParam(
//       {required this.currentIndex,
//       required this.objs,
//       this.keywords,
//       this.page = 1});
// }

// class ShortVideoPlayPage extends StatefulWidget {
//   final ShortVideoPageParam param;

//   const ShortVideoPlayPage({super.key, required this.param});

//   @override
//   State<ShortVideoPlayPage> createState() => _ShortVideoPlayPageState();
// }

// class _ShortVideoPlayPageState extends State<ShortVideoPlayPage> {
//   late ShortVideoPlayPageController controller;

//   @override
//   void initState() {
//     super.initState();
//     controller =
//         ShortVideoPlayPageController(param: widget.param, setState: setState);
//   }

//   @override
//   void dispose() {
//     super.dispose();
//     controller.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Swiper(
//         controller: controller.swiperController,
//         loop: false,
//         itemCount: controller.objs.length,
//         scrollDirection: Axis.vertical,
//         index: controller.currentIndex,
//         itemBuilder: (_, index) {
//           ListObj obj = controller.objs[index];
//           return ShortVideoPlayer(
//             videoUrl: ImageUtil.getImageUrl(obj.videoUrl ?? ""),
//             videoImageUrl: ImageUtil.getImageUrl(obj.imgUrl ?? ""),
//             onFinish: () {
//               controller.swiperController.next();
//             },
//           );
//         },
//         onIndexChanged: (int index) async {
//           controller.onIndexChanged(index: index);
//         });
//   }
// }

// class ShortVideoPlayPageController {
//   final ShortVideoPageParam param;
//   int currentIndex;
//   List<ListObj> objs;
//   void Function(VoidCallback) setState;
//   bool hasMore = true;
//   SwiperController swiperController = SwiperController();

//   ShortVideoPlayPageController({required this.param, required this.setState})
//       : currentIndex = param.currentIndex,
//         objs = param.objs;

//   void onIndexChanged({required int index}) async {
//     bool isLast = index == objs.length - 1;
//     if (isLast) {
//       _loadMore();
//     }
//     //缓存下一个视频图片
//     ListObj nextObj = objs[index];
//     //todo 还没确认到底有没有效果
//     precacheImage(
//         CachedNetworkImageProvider(ImageUtil.getImageUrl(nextObj.imgUrl ?? "")),
//         MApplication.getContext());
//   }

//   void _loadMore() async {
//     if (!hasMore) {
//       //没有了
//       return;
//     }
//     param.page = param.page + 1;
//     await ApiRepository.pageappshortvideodata(
//             pageNo: param.page + 1, name: param.keywords)
//         .then((response) {
//       if (response.isSuccess && response.isDataNotNull) {
//         var result = response.dataNotNull.list ?? [];
//         num? pages = response.dataNotNull.pages;
//         if (pages != null) {
//           if (pages > param.page) {
//             hasMore = true;
//           } else {
//             hasMore = false;
//           }
//         }

//         var list = result.where((element) => element.isWechat != '1').toList();
//         if (list.isNotEmpty) {
//           objs.addAll(list);
//           setState.call(() {});
//         } else {
//           hasMore = false;
//         }
//       }
//     });
//   }

//   dispose() {
//     swiperController.dispose();
//   }
// }
