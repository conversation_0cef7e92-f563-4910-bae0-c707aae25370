// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:lib_base/widgets/image/net_cache_image.dart';
// import 'package:lib_base/widgets/text_label_tip.dart';
// import 'package:yyb_flutter/src/extention/build_context_ext.dart';
// import 'package:yyb_flutter/model/http/english_interest_info.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';

// class UpModuleWidget extends StatelessWidget {
//   final Obj obj;
//   final GestureTapCallback? onTap;
//   const UpModuleWidget({super.key, required this.obj, this.onTap});

//   @override
//   Widget build(BuildContext context) {
//     String image = obj.imageUrl ?? "";
//     String imageUrl = ImageUtil.getImageUrl(image);
//     double width = 100.r;
//     bool isHot = obj.hotFlag == '1';
//     bool isNew = obj.newFlag == '1';
//     return InkWell(
//       onTap: onTap,
//       child: Padding(
//         padding: EdgeInsets.only(right: 15.r),
//         child: Stack(
//           children: [
//             BaseNetCacheImage(
//               imageUrl: imageUrl,
//               width: width,
//             ),
//             Positioned(
//                 top: 0,
//                 right: 0,
//                 child: Row(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     isNew
//                         ? TextLabelTip.simpleLabel(
//                             text: context.string.textOfNew)
//                         : SizedBox.shrink(),
//                     isHot
//                         ? TextLabelTip.simpleLabel(
//                             text: context.string.textOfHot)
//                         : SizedBox.shrink(),
//                   ],
//                 ))
//           ],
//         ),
//       ),
//     );
//   }
// }
