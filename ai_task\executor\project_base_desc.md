

项目概述
这是一个名为"yyb_flutter"的大型Flutter项目，主要用于教育类应用开发。项目采用模块化架构设计，主项目通过多个功能模块(package)组织代码，当前版本为8.2.7+827。

技术栈
主框架: Flutter (Dart)
状态管理: Riverpod (2.3.1版本)
路由管理: go_router
网络请求: dio (5.4.2+1)
UI适配: flutter_screenutil (5.9.0)
代码生成: build_runner, json_serializable, riverpod_generator
项目结构
核心目录
lib/: 主应用代码
package/: 功能模块目录，包含多个独立的功能模块
lib_base/: 基础库模块，提供通用组件和工具类
assets/: 应用资源文件（图片、字体等）

 
# 开发规范
## 路由规范
项目使用go_router进行路由管理，每个模块都有自己的路由配置文件。主项目的route_define.dart文件整合了所有模块的路由。
你不需要管路由相关的内容,路由由我自己去配置

### 模块化架构
项目采用模块化设计，每个功能模块都是独立的package 

## 状态管理
使用Riverpod进行状态管理，结合代码生成工具(riverpod_generator)使用注解方式定义Provider。
 
## 主题与样式
项目有统一的主题配置，可以通过ThemeConfig.currentTheme访问颜色、字体等样式资源。

## 图片资源
使用flutter_gen工具管理图片资源，每个模块管理自己的图片资源，公共图片放在lib_base模块中。
 

## mvc 模式
项目的每个page都会对应一个controller， controller会处理页面的逻辑，并调用view进行页面的更新。
每个页面都有一个文件夹， 比如 student_work_list_page 页面， 那么它对应的结构就是：
student_work_list_page
    - student_work_list_page.dart
    - controller
        student_work_list_controller.dart // riverpode 对应的provider， @riverpod 注解， 通过riverpod_generator辅助
    - widget
        - 页面对应的抽取出来的widget
    - page
        - 页面的子页面

## 不要执行build_runner命令,  你自己去生成该命令最终会生成的那个.g.dart文件