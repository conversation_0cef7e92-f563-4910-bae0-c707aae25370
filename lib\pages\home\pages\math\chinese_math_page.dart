// import 'package:flutter/cupertino.dart';
// import 'package:lib_base/widgets/common/base_app_bar.dart';
// import 'package:lib_base/widgets/common/base_scaffold.dart';
// import 'package:lib_base/widgets/common/keep_alive_wrapper.dart';
// import 'package:yyb_flutter/pages/home/<USER>/math/math_app_bar/chinese_math_app_bar.dart';
// import 'package:yyb_flutter/pages/home/<USER>/math/pages/chinese/chinese_page.dart';
// import 'package:yyb_flutter/pages/home/<USER>/math/pages/math/math_page.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:yyb_flutter/providers/chinese_math_page_tab_provider.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';

// ///语文，数学页面
// class ChineseMathPage extends ConsumerWidget {
//   const ChineseMathPage({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     List<Widget> pages = [
//       KeepAliveWrapper(child: ChinesePage()),
//       KeepAliveWrapper(child: MathPage()),
//     ];
//     var notifier = ref.read(chineseMathPageTabNotifierProvider.notifier);
//     return BaseScaffold(
//       appBar: BaseAppBar.customAppBar(
//         title: ChineseMathAppBar(),
//       ),
//       body: Padding(
//         padding: EdgeInsets.symmetric(horizontal: 15.w),
//         child: PageView.builder(
//           controller: notifier.pageController,
//           itemBuilder: (_, index) {
//             return pages[index];
//           },
//           itemCount: pages.length,
//           onPageChanged: (index) {
//             notifier.doSwitch(index);
//           },
//           physics: NeverScrollableScrollPhysics(),
//         ),
//       ),
//     );
//   }
// }
