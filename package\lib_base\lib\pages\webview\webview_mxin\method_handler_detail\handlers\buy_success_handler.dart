import 'package:flutter_inappwebview/src/in_app_webview/in_app_webview_controller.dart';
import 'package:flutter_riverpod/src/consumer.dart';
import 'package:lib_base/model/js_bridge_bean.dart';
import 'package:lib_base/model/js_bridge_data_bean.dart';
import 'package:lib_base/pages/webview/webview_mxin/method_handler_detail/method_handler_detail.dart';
import 'package:lib_base/providers/resource_state_provider.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';

class BuySuccessHandler extends EyybAssetWebHandler {
  @override
  handlerCallback(InAppWebViewController webViewController, WidgetRef ref,
      JsBridgeBean? jsBridgeBean, List<dynamic> arguments) async {
    if (jsBridgeBean?.data != null) {
      JsBridgeDataBean data = jsBridgeBean!.data!;

      //刷新用户信息
      ref.read(userInfoNotifierProvider.notifier).refreshInfo();
      // 刷新所有需要更新状态的模块
      ref.read(resourceStateProviderProvider.notifier).rereshState();
    }
  }

  @override
  String get methodName => 'buySuccess';
}
