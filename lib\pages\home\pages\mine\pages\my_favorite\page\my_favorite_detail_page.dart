import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/widgets/common/base_app_bar.dart';
import 'package:lib_base/widgets/common/base_scaffold.dart';

class MyFavoriteDetailPage extends ConsumerStatefulWidget {
  const MyFavoriteDetailPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _MyFavoriteDetailPageState();
}

class _MyFavoriteDetailPageState extends ConsumerState<MyFavoriteDetailPage> {
  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      appBar: BaseAppBar.normalAppBar(titleText: "收藏详情"),
      body: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: 20.h),
            Text('收藏详情内容'),
          ],
        ),
      ),
    );
  }
}