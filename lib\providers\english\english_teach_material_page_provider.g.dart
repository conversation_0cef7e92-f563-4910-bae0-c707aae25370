// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'english_teach_material_page_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$englishTeachMaterialPageNotifierHash() =>
    r'd5facd40e1ed34ff358948cac001f55dd40f28ab';

/// See also [EnglishTeachMaterialPageNotifier].
@ProviderFor(EnglishTeachMaterialPageNotifier)
final englishTeachMaterialPageNotifierProvider =
    AutoDisposeNotifierProvider<EnglishTeachMaterialPageNotifier, int>.internal(
  EnglishTeachMaterialPageNotifier.new,
  name: r'englishTeachMaterialPageNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$englishTeachMaterialPageNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$EnglishTeachMaterialPageNotifier = AutoDisposeNotifier<int>;
// ignore_for_file: unnecessary_raw_strings, subtype_of_sealed_class, invalid_use_of_internal_member, do_not_use_environment, prefer_const_constructors, public_member_api_docs, avoid_private_typedef_functions
