// import 'package:flutter/cupertino.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/config/theme_config.dart';
// import 'package:lib_base/model/http/book_study_record_vo.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:lib_base/widgets/image/net_cache_image.dart';

// class ColumnItemDefault extends StatelessWidget {
//   final BookModuleInfoList item;
//   const ColumnItemDefault({super.key, required this.item});

//   @override
//   Widget build(BuildContext context) {
//     String image = item.stuIcon ?? "";
//     String imageUrl = ImageUtil.getImageUrl(image);
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Padding(
//           padding: EdgeInsets.only(bottom: 5.r),
//           child: BaseNetCacheImage(
//             imageUrl: imageUrl,
//             width: 40.r,
//             height: 40.r,
//           ),
//         ),
//         Text(
//           item.name ?? "",
//           style: ThemeConfig.currentTheme.text14,
//         ),
//       ],
//     );
//   }
// }

// class ColumnItemType1 extends StatelessWidget {
//   final BookModuleInfoList item;
//   const ColumnItemType1({super.key, required this.item});

//   @override
//   Widget build(BuildContext context) {
//     String image = item.stuIcon ?? "";
//     String imageUrl = ImageUtil.getImageUrl(image);
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Padding(
//           padding: EdgeInsets.only(bottom: 5.r),
//           child: BaseNetCacheImage(
//             imageUrl: imageUrl,
//             width: 40.r,
//             height: 40.r,
//           ),
//         ),
//         Text(
//           item.name ?? "",
//           style: ThemeConfig.currentTheme.text14,
//         ),
//       ],
//     );
//   }
// }

// class ColumnItemType2 extends StatelessWidget {
//   final BookModuleInfoList item;
//   const ColumnItemType2({super.key, required this.item});

//   @override
//   Widget build(BuildContext context) {
//     String image = item.stuIcon ?? "";
//     String imageUrl = ImageUtil.getImageUrl(image);
//     return Container(
//       padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 7.h),
//       decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(10.r),
//           color: ThemeConfig.currentTheme.colorWhite,
//           boxShadow: [
//             BoxShadow(
//                 color: Color(0xffCECECE).withOpacity(0.16),
//                 offset: Offset(0, 1.5))
//           ]),
//       child: Row(
//         children: [
//           Padding(
//             padding: EdgeInsets.only(right: 12.5.r),
//             child: BaseNetCacheImage(
//               imageUrl: imageUrl,
//               width: 40.r,
//               height: 40.r,
//             ),
//           ),
//           Expanded(
//               child: Text(
//             item.name ?? "",
//             style: ThemeConfig.currentTheme.text17,
//           )),
//         ],
//       ),
//     );
//   }
// }

// class ColumnItemType3 extends StatelessWidget {
//   final BookModuleInfoList item;
//   const ColumnItemType3({super.key, required this.item});

//   @override
//   Widget build(BuildContext context) {
//     String image = item.stuIcon ?? "";
//     String imageUrl = ImageUtil.getImageUrl(image);
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Padding(
//           padding: EdgeInsets.only(bottom: 5.r),
//           child: BaseNetCacheImage(
//             imageUrl: imageUrl,
//             width: 330.r,
//             height: 40.r,
//           ),
//         ),
//         Text(
//           item.name ?? "",
//           style: ThemeConfig.currentTheme.text14,
//         ),
//       ],
//     );
//   }
// }
