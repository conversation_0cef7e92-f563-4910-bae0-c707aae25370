// import 'package:flutter/cupertino.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/config/theme_config.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:lib_base/widgets/image/net_cache_image.dart';
// import 'package:yyb_flutter/model/http/query_chinese_expand_model.dart';

// class ExpandModelCard extends StatelessWidget {
//   final List<Obj> objs;
//   const ExpandModelCard({super.key, required this.objs});

//   @override
//   Widget build(BuildContext context) {
//     List<Widget> children = [];
//     for (Obj obj in objs) {
//       children.add(ExpandModelItemWidget(obj: obj));
//     }
//     return Container(
//       padding: EdgeInsets.only(left: 19.w, right: 19.w, bottom: 16.h),
//       decoration: BoxDecoration(
//           color: ThemeConfig.currentTheme.colorWhite,
//           borderRadius: BorderRadius.circular(10.r)),
//       child: Wrap(
//         children: children,
//       ),
//     );
//   }
// }

// class ExpandModelItemWidget extends StatelessWidget {
//   final Obj obj;
//   const ExpandModelItemWidget({super.key, required this.obj});

//   @override
//   Widget build(BuildContext context) {
//     String image = obj.indexImg ?? "";
//     String imageUrl = ImageUtil.getImageUrl(image);
//     return Container(
//       width: (1.sw - 38.w - 30.w) / 4,
//       padding: EdgeInsets.only(top: 16.h),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Padding(
//             padding: EdgeInsets.only(bottom: 5.r),
//             child: BaseNetCacheImage(
//               imageUrl: imageUrl,
//               width: 40.r,
//               height: 40.r,
//             ),
//           ),
//           Text(
//             obj.name ?? "",
//             style: ThemeConfig.currentTheme.text14,
//           ),
//         ],
//       ),
//     );
//   }
// }
