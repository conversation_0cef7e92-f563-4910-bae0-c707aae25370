// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:lib_base/config/theme_config.dart';
// import 'package:lib_base/log/log.dart';
// import 'package:lib_base/model/http/book_study_record_vo.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:lib_base/widgets/image/net_cache_image.dart';
// import 'package:yyb_flutter/providers/chinese_math/math/math_page_provider.dart';

// class ModuleItemWrap extends ConsumerWidget {
//   final List<BookModuleInfoList> modules;
//   const ModuleItemWrap({
//     super.key,
//     required this.modules,
//   });

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     MathPageNotifier controller = ref.read(mathPageNotifierProvider.notifier);
//     List<Widget> children = [];
//     for (BookModuleInfoList m in modules) {
//       String image = m.stuIcon ?? "";
//       String imageUrl = ImageUtil.getImageUrl(image);
//       if (m.moduleId == "95") {
//         continue;
//       }
//       children.add(InkWell(
//           onTap: () {
//             controller.toModule(m);
//           },
//           child: BaseNetCacheImage(
//             imageUrl: imageUrl,
//             width: (1.sw - 30.w - 28.w) / 2 - 10.w,
//           )));
//     }
//     return Container(
//       padding: EdgeInsets.symmetric(vertical: 17.5.h, horizontal: 14.w),
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(10.r),
//         color: ThemeConfig.currentTheme.colorWhite,
//       ),
//       child: Wrap(
//         spacing: 10.w,
//         runSpacing: 10.h,
//         children: children,
//       ),
//     );
//   }
// }
