// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'expand_video_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ExpandVideoItem _$ExpandVideoItemFromJson(Map<String, dynamic> json) =>
    ExpandVideoItem(
      id: json['id'] as String?,
      saveFilePath: json['saveFilePath'] as String?,
      downLoadId: json['downLoadId'] as int?,
      sqlId: json['sqlId'] as int?,
      downLoadProgress: json['downLoadProgress'] as int?,
      downLoadStatus: json['downLoadStatus'] as int?,
      coverImage: json['coverImage'] as String?,
      name: json['name'] as String?,
      msg: json['msg'] as String?,
      needUpdate: json['needUpdate'] as bool?,
      createDate: json['createDate'] as int?,
      updateDate: json['updateDate'] as int?,
      subName: json['subName'] as String?,
      bgkImg: json['bgkImg'] as String?,
      playTimes: json['playTimes'] as String?,
      themeId: json['themeId'] as String?,
      videoPath: json['videoPath'] as String?,
      videoSimplePath: json['videoSimplePath'] as String?,
      collectStatus: json['collectStatus'] as bool?,
      sort: json['sort'] as int?,
      resourceFrom: json['resourceFrom'] as int?,
      videoSourcePath: json['videoSourcePath'] as String?,
      playTimesZh: json['playTimesZh'] as String?,
      remarks: json['remarks'] as String?,
      time: json['time'] as int?,
      videoSize: json['videoSize'] as int?,
      videoDesc: json['videoDesc'] as String?,
      shareType: json['shareType'] as String?,
      expModuleId: json['expModuleId'] as String?,
    );

Map<String, dynamic> _$ExpandVideoItemToJson(ExpandVideoItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'saveFilePath': instance.saveFilePath,
      'downLoadId': instance.downLoadId,
      'sqlId': instance.sqlId,
      'downLoadProgress': instance.downLoadProgress,
      'downLoadStatus': instance.downLoadStatus,
      'coverImage': instance.coverImage,
      'name': instance.name,
      'msg': instance.msg,
      'needUpdate': instance.needUpdate,
      'createDate': instance.createDate,
      'updateDate': instance.updateDate,
      'subName': instance.subName,
      'bgkImg': instance.bgkImg,
      'playTimes': instance.playTimes,
      'themeId': instance.themeId,
      'videoPath': instance.videoPath,
      'videoSimplePath': instance.videoSimplePath,
      'collectStatus': instance.collectStatus,
      'sort': instance.sort,
      'resourceFrom': instance.resourceFrom,
      'videoSourcePath': instance.videoSourcePath,
      'playTimesZh': instance.playTimesZh,
      'remarks': instance.remarks,
      'time': instance.time,
      'videoSize': instance.videoSize,
      'videoDesc': instance.videoDesc,
      'shareType': instance.shareType,
      'expModuleId': instance.expModuleId,
    };
