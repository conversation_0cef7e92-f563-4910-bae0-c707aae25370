// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'exercise_report_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$exerciseReportPageControllerHash() =>
    r'154eb632a5dffb1ff136d84f3872b004077a5d09';

/// See also [ExerciseReportPageController].
@ProviderFor(ExerciseReportPageController)
final exerciseReportPageControllerProvider =
    AutoDisposeNotifierProvider<ExerciseReportPageController, int>.internal(
  ExerciseReportPageController.new,
  name: r'exerciseReportPageControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$exerciseReportPageControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ExerciseReportPageController = AutoDisposeNotifier<int>;
// ignore_for_file: unnecessary_raw_strings, subtype_of_sealed_class, invalid_use_of_internal_member, do_not_use_environment, prefer_const_constructors, public_member_api_docs, avoid_private_typedef_functions
