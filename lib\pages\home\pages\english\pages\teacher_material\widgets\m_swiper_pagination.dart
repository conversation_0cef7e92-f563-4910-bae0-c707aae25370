// import 'package:card_swiper/card_swiper.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:lib_base/log/log.dart';

// class MSwiperPagination extends DotSwiperPaginationBuilder {
//   MSwiperPagination({super.color, super.activeColor});

//   @override
//   Widget build(BuildContext context, SwiperPluginConfig config) {
//     if (config.itemCount > 20) {
//       Logger.info(
//         'The itemCount is too big, we suggest use FractionPaginationBuilder '
//         'instead of DotSwiperPaginationBuilder in this situation',
//       );
//     }
//     var activeColor = this.activeColor;
//     var color = this.color;

//     if (activeColor == null || color == null) {
//       final themeData = Theme.of(context);
//       activeColor = this.activeColor ?? themeData.primaryColor;
//       color = this.color ?? themeData.scaffoldBackgroundColor;
//     }

//     if (config.indicatorLayout != PageIndicatorLayout.NONE &&
//         config.layout == SwiperLayout.DEFAULT) {
//       return PageIndicator(
//         count: config.itemCount,
//         controller: config.pageController!,
//         layout: config.indicatorLayout,
//         size: size,
//         activeColor: activeColor,
//         color: color,
//         space: space,
//       );
//     }

//     final list = <Widget>[];

//     final itemCount = config.itemCount;
//     final activeIndex = config.activeIndex;

//     for (var i = 0; i < itemCount; ++i) {
//       final active = i == activeIndex;
//       list.add(Container(
//         key: Key('pagination_$i'),
//         margin: EdgeInsets.all(space),
//         child: Container(
//           width: 10,
//           height: 5,
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(10),
//             color: active ? activeColor : color,
//           ),
//         ),
//       ));
//     }

//     if (config.scrollDirection == Axis.vertical) {
//       return Column(
//         key: key,
//         mainAxisSize: MainAxisSize.min,
//         children: list,
//       );
//     } else {
//       return Row(
//         key: key,
//         mainAxisSize: MainAxisSize.min,
//         children: list,
//       );
//     }
//   }
// }
